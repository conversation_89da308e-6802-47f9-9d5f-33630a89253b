'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import Header from '@/components/layout/Header'
import { ArrowLeft, Download, Copy, Save, FileText } from 'lucide-react'
import { supabase, CoverLetter } from '@/lib/supabase'
import { exportToPDF, exportToText, copyToClipboard } from '@/lib/pdf-export'

export default function CoverLetterPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const [coverLetter, setCoverLetter] = useState<CoverLetter | null>(null)
  const [loadingLetter, setLoadingLetter] = useState(true)
  const [editing, setEditing] = useState(false)
  const [editedContent, setEditedContent] = useState('')
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user && params.id) {
      fetchCoverLetter()
    }
  }, [user, params.id])

  const fetchCoverLetter = async () => {
    try {
      const { data, error } = await supabase
        .from('cover_letters')
        .select('*')
        .eq('id', params.id)
        .eq('user_id', user?.id)
        .single()

      if (error) {
        console.error('Error fetching cover letter:', error)
        router.push('/dashboard')
        return
      }

      setCoverLetter(data)
      setEditedContent(data.content)
    } catch (error) {
      console.error('Error fetching cover letter:', error)
      router.push('/dashboard')
    } finally {
      setLoadingLetter(false)
    }
  }

  const handleSave = async () => {
    if (!coverLetter) return

    setSaving(true)

    try {
      const { error } = await supabase
        .from('cover_letters')
        .update({ 
          content: editedContent,
          updated_at: new Date().toISOString()
        })
        .eq('id', coverLetter.id)

      if (error) {
        throw error
      }

      setCoverLetter({ ...coverLetter, content: editedContent })
      setEditing(false)
    } catch (error) {
      console.error('Error saving cover letter:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleExportPDF = () => {
    if (!coverLetter) return

    exportToPDF({
      title: coverLetter.title,
      content: coverLetter.content,
      userInfo: {
        name: user?.full_name,
        email: user?.email
      }
    })
  }

  const handleExportText = () => {
    if (!coverLetter) return
    exportToText(coverLetter.content, coverLetter.title)
  }

  const handleCopyToClipboard = async () => {
    if (!coverLetter) return

    try {
      await copyToClipboard(coverLetter.content)
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  if (loading || loadingLetter) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user || !coverLetter) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/dashboard')}
              className="btn-tertiary"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </button>
            <div>
              <h1 className="text-heading-1 text-gray-900">{coverLetter.title}</h1>
              <p className="text-gray-600">
                Created {new Date(coverLetter.created_at).toLocaleDateString()} • 
                Style: {coverLetter.style}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {editing ? (
              <>
                <button
                  onClick={() => {
                    setEditing(false)
                    setEditedContent(coverLetter.content)
                  }}
                  className="btn-secondary"
                  disabled={saving}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="btn-primary flex items-center space-x-2"
                  disabled={saving}
                >
                  <Save className="h-4 w-4" />
                  <span>{saving ? 'Saving...' : 'Save'}</span>
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setEditing(true)}
                  className="btn-secondary"
                >
                  Edit
                </button>
                <div className="relative">
                  <button className="btn-primary flex items-center space-x-2">
                    <Download className="h-4 w-4" />
                    <span>Export</span>
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50 hidden group-hover:block">
                    <button
                      onClick={handleExportPDF}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Export as PDF
                    </button>
                    <button
                      onClick={handleExportText}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Export as Text
                    </button>
                    <button
                      onClick={handleCopyToClipboard}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Copy to Clipboard
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cover Letter Content */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-heading-2 text-gray-900">Cover Letter</h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleExportPDF}
                    className="btn-tertiary"
                    title="Export as PDF"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  <button
                    onClick={handleCopyToClipboard}
                    className="btn-tertiary"
                    title="Copy to clipboard"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>
              
              {editing ? (
                <textarea
                  value={editedContent}
                  onChange={(e) => setEditedContent(e.target.value)}
                  className="input-field h-96 font-serif"
                  placeholder="Edit your cover letter content..."
                />
              ) : (
                <div className="prose max-w-none">
                  <div className="whitespace-pre-wrap font-serif text-gray-900 leading-relaxed">
                    {coverLetter.content}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Details */}
            <div className="card">
              <h3 className="text-heading-3 text-gray-900 mb-4">Details</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <p className={`text-sm mt-1 px-2 py-1 rounded-full inline-block ${
                    coverLetter.status === 'completed' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {coverLetter.status}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Style</label>
                  <p className="text-sm text-gray-900 mt-1 capitalize">{coverLetter.style}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Created</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {new Date(coverLetter.created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Last Updated</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {new Date(coverLetter.updated_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            </div>

            {/* Job Description */}
            {coverLetter.job_description && (
              <div className="card">
                <h3 className="text-heading-3 text-gray-900 mb-4">Job Description</h3>
                <div className="text-sm text-gray-600 max-h-32 overflow-y-auto">
                  {coverLetter.job_description}
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="card">
              <h3 className="text-heading-3 text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-2">
                <button
                  onClick={() => router.push('/create')}
                  className="btn-secondary w-full justify-center"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Create New Cover Letter
                </button>
                <button
                  onClick={handleExportPDF}
                  className="btn-secondary w-full justify-center"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
