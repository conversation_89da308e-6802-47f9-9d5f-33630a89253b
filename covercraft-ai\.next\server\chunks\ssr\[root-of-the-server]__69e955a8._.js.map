{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogOut, User, CreditCard } from 'lucide-react'\nimport { useState } from 'react'\n\nexport default function Header() {\n  const { user, signOut } = useAuth()\n  const [showDropdown, setShowDropdown] = useState(false)\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  const getTierBadgeColor = (tier: string) => {\n    switch (tier) {\n      case 'premium':\n        return 'bg-purple-100 text-purple-800'\n      case 'standard':\n        return 'bg-blue-100 text-blue-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <header className=\"bg-white border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <h1 className=\"text-2xl font-bold text-blue-600\">CoverCraft AI</h1>\n          </div>\n\n          {/* User Menu */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Credits Display */}\n            {user && (\n              <div className=\"flex items-center space-x-2\">\n                <CreditCard className=\"h-5 w-5 text-gray-400\" />\n                <span className=\"text-sm text-gray-600\">\n                  {user.subscription_tier === 'free' \n                    ? `${user.credits_remaining} credits left`\n                    : 'Unlimited'\n                  }\n                </span>\n                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTierBadgeColor(user.subscription_tier)}`}>\n                  {user.subscription_tier.charAt(0).toUpperCase() + user.subscription_tier.slice(1)}\n                </span>\n              </div>\n            )}\n\n            {/* User Dropdown */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowDropdown(!showDropdown)}\n                className=\"flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none\"\n              >\n                <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <User className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <span className=\"text-sm font-medium\">{user?.full_name || user?.email}</span>\n              </button>\n\n              {showDropdown && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\">\n                  <div className=\"px-4 py-2 border-b border-gray-100\">\n                    <p className=\"text-sm font-medium text-gray-900\">{user?.full_name}</p>\n                    <p className=\"text-sm text-gray-500\">{user?.email}</p>\n                  </div>\n                  \n                  <button\n                    onClick={() => {\n                      setShowDropdown(false)\n                      // Navigate to profile settings\n                    }}\n                    className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    <span>Profile Settings</span>\n                  </button>\n                  \n                  <button\n                    onClick={() => {\n                      setShowDropdown(false)\n                      // Navigate to subscription\n                    }}\n                    className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                  >\n                    <CreditCard className=\"h-4 w-4\" />\n                    <span>Subscription</span>\n                  </button>\n                  \n                  <hr className=\"my-1\" />\n                  \n                  <button\n                    onClick={handleSignOut}\n                    className=\"w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 flex items-center space-x-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span>Sign out</span>\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;kCAInD,8OAAC;wBAAI,WAAU;;4BAEZ,sBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAK,WAAU;kDACb,KAAK,iBAAiB,KAAK,SACxB,GAAG,KAAK,iBAAiB,CAAC,aAAa,CAAC,GACxC;;;;;;kDAGN,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAAE,kBAAkB,KAAK,iBAAiB,GAAG;kDACvG,KAAK,iBAAiB,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC;;;;;;;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DAAuB,MAAM,aAAa,MAAM;;;;;;;;;;;;oCAGjE,8BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC,MAAM;;;;;;kEACxD,8OAAC;wDAAE,WAAU;kEAAyB,MAAM;;;;;;;;;;;;0DAG9C,8OAAC;gDACC,SAAS;oDACP,gBAAgB;gDAChB,+BAA+B;gDACjC;gDACA,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC;gDACC,SAAS;oDACP,gBAAgB;gDAChB,2BAA2B;gDAC7B;gDACA,WAAU;;kEAEV,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC;gDAAG,WAAU;;;;;;0DAEd,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/lib/pdf-export.ts"], "sourcesContent": ["import jsPDF from 'jspdf'\n\nexport interface ExportOptions {\n  title: string\n  content: string\n  userInfo?: {\n    name?: string\n    email?: string\n    phone?: string\n    address?: string\n  }\n}\n\nexport function exportToPDF({ title, content, userInfo }: ExportOptions) {\n  const doc = new jsPDF()\n  \n  // Set up fonts and styling\n  const pageWidth = doc.internal.pageSize.getWidth()\n  const pageHeight = doc.internal.pageSize.getHeight()\n  const margin = 20\n  const maxWidth = pageWidth - (margin * 2)\n  \n  let yPosition = margin\n\n  // Add user info header if provided\n  if (userInfo) {\n    doc.setFontSize(12)\n    doc.setFont('helvetica', 'normal')\n    \n    if (userInfo.name) {\n      doc.text(userInfo.name, margin, yPosition)\n      yPosition += 6\n    }\n    \n    if (userInfo.email) {\n      doc.text(userInfo.email, margin, yPosition)\n      yPosition += 6\n    }\n    \n    if (userInfo.phone) {\n      doc.text(userInfo.phone, margin, yPosition)\n      yPosition += 6\n    }\n    \n    if (userInfo.address) {\n      doc.text(userInfo.address, margin, yPosition)\n      yPosition += 6\n    }\n    \n    yPosition += 10 // Extra space after header\n  }\n\n  // Add date\n  const today = new Date().toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n  \n  doc.setFontSize(11)\n  doc.text(today, margin, yPosition)\n  yPosition += 15\n\n  // Process and add content\n  doc.setFontSize(11)\n  doc.setFont('helvetica', 'normal')\n  \n  // Split content into paragraphs\n  const paragraphs = content.split('\\n\\n').filter(p => p.trim())\n  \n  paragraphs.forEach((paragraph, index) => {\n    // Clean up the paragraph\n    const cleanParagraph = paragraph.trim()\n    \n    if (cleanParagraph) {\n      // Split text to fit within margins\n      const lines = doc.splitTextToSize(cleanParagraph, maxWidth)\n      \n      // Check if we need a new page\n      if (yPosition + (lines.length * 5) > pageHeight - margin) {\n        doc.addPage()\n        yPosition = margin\n      }\n      \n      // Add the paragraph\n      lines.forEach((line: string) => {\n        doc.text(line, margin, yPosition)\n        yPosition += 5\n      })\n      \n      // Add space between paragraphs\n      yPosition += 5\n    }\n  })\n\n  // Save the PDF\n  const fileName = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`\n  doc.save(fileName)\n}\n\nexport function exportToText(content: string, title: string) {\n  const blob = new Blob([content], { type: 'text/plain' })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\nexport function copyToClipboard(content: string) {\n  if (navigator.clipboard && window.isSecureContext) {\n    return navigator.clipboard.writeText(content)\n  } else {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea')\n    textArea.value = content\n    textArea.style.position = 'fixed'\n    textArea.style.left = '-999999px'\n    textArea.style.top = '-999999px'\n    document.body.appendChild(textArea)\n    textArea.focus()\n    textArea.select()\n    \n    return new Promise<void>((resolve, reject) => {\n      if (document.execCommand('copy')) {\n        resolve()\n      } else {\n        reject(new Error('Failed to copy to clipboard'))\n      }\n      document.body.removeChild(textArea)\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAaO,SAAS,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAiB;IACrE,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK;IAErB,2BAA2B;IAC3B,MAAM,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ;IAChD,MAAM,aAAa,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS;IAClD,MAAM,SAAS;IACf,MAAM,WAAW,YAAa,SAAS;IAEvC,IAAI,YAAY;IAEhB,mCAAmC;IACnC,IAAI,UAAU;QACZ,IAAI,WAAW,CAAC;QAChB,IAAI,OAAO,CAAC,aAAa;QAEzB,IAAI,SAAS,IAAI,EAAE;YACjB,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,QAAQ;YAChC,aAAa;QACf;QAEA,IAAI,SAAS,KAAK,EAAE;YAClB,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,QAAQ;YACjC,aAAa;QACf;QAEA,IAAI,SAAS,KAAK,EAAE;YAClB,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,QAAQ;YACjC,aAAa;QACf;QAEA,IAAI,SAAS,OAAO,EAAE;YACpB,IAAI,IAAI,CAAC,SAAS,OAAO,EAAE,QAAQ;YACnC,aAAa;QACf;QAEA,aAAa,GAAG,2BAA2B;;IAC7C;IAEA,WAAW;IACX,MAAM,QAAQ,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACnD,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,IAAI,WAAW,CAAC;IAChB,IAAI,IAAI,CAAC,OAAO,QAAQ;IACxB,aAAa;IAEb,0BAA0B;IAC1B,IAAI,WAAW,CAAC;IAChB,IAAI,OAAO,CAAC,aAAa;IAEzB,gCAAgC;IAChC,MAAM,aAAa,QAAQ,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI;IAE3D,WAAW,OAAO,CAAC,CAAC,WAAW;QAC7B,yBAAyB;QACzB,MAAM,iBAAiB,UAAU,IAAI;QAErC,IAAI,gBAAgB;YAClB,mCAAmC;YACnC,MAAM,QAAQ,IAAI,eAAe,CAAC,gBAAgB;YAElD,8BAA8B;YAC9B,IAAI,YAAa,MAAM,MAAM,GAAG,IAAK,aAAa,QAAQ;gBACxD,IAAI,OAAO;gBACX,YAAY;YACd;YAEA,oBAAoB;YACpB,MAAM,OAAO,CAAC,CAAC;gBACb,IAAI,IAAI,CAAC,MAAM,QAAQ;gBACvB,aAAa;YACf;YAEA,+BAA+B;YAC/B,aAAa;QACf;IACF;IAEA,eAAe;IACf,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC;IACzE,IAAI,IAAI,CAAC;AACX;AAEO,SAAS,aAAa,OAAe,EAAE,KAAa;IACzD,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAa;IACtD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG,GAAG,MAAM,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC;IACxE,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAEO,SAAS,gBAAgB,OAAe;IAC7C,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;QACjD,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;IACvC,OAAO;QACL,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;QAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;QACtB,SAAS,KAAK,CAAC,GAAG,GAAG;QACrB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QAEf,OAAO,IAAI,QAAc,CAAC,SAAS;YACjC,IAAI,SAAS,WAAW,CAAC,SAAS;gBAChC;YACF,OAAO;gBACL,OAAO,IAAI,MAAM;YACnB;YACA,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;AACF", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/app/cover-letters/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useEffect, useState } from 'react'\nimport { useRouter, useParams } from 'next/navigation'\nimport Header from '@/components/layout/Header'\nimport { ArrowLeft, Download, Copy, Save, FileText } from 'lucide-react'\nimport { supabase, CoverLetter } from '@/lib/supabase'\nimport { exportToPDF, exportToText, copyToClipboard } from '@/lib/pdf-export'\n\nexport default function CoverLetterPage() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n  const params = useParams()\n  const [coverLetter, setCoverLetter] = useState<CoverLetter | null>(null)\n  const [loadingLetter, setLoadingLetter] = useState(true)\n  const [editing, setEditing] = useState(false)\n  const [editedContent, setEditedContent] = useState('')\n  const [saving, setSaving] = useState(false)\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth')\n    }\n  }, [user, loading, router])\n\n  useEffect(() => {\n    if (user && params.id) {\n      fetchCoverLetter()\n    }\n  }, [user, params.id])\n\n  const fetchCoverLetter = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('cover_letters')\n        .select('*')\n        .eq('id', params.id)\n        .eq('user_id', user?.id)\n        .single()\n\n      if (error) {\n        console.error('Error fetching cover letter:', error)\n        router.push('/dashboard')\n        return\n      }\n\n      setCoverLetter(data)\n      setEditedContent(data.content)\n    } catch (error) {\n      console.error('Error fetching cover letter:', error)\n      router.push('/dashboard')\n    } finally {\n      setLoadingLetter(false)\n    }\n  }\n\n  const handleSave = async () => {\n    if (!coverLetter) return\n\n    setSaving(true)\n\n    try {\n      const { error } = await supabase\n        .from('cover_letters')\n        .update({ \n          content: editedContent,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', coverLetter.id)\n\n      if (error) {\n        throw error\n      }\n\n      setCoverLetter({ ...coverLetter, content: editedContent })\n      setEditing(false)\n    } catch (error) {\n      console.error('Error saving cover letter:', error)\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleExportPDF = () => {\n    if (!coverLetter) return\n\n    exportToPDF({\n      title: coverLetter.title,\n      content: coverLetter.content,\n      userInfo: {\n        name: user?.full_name,\n        email: user?.email\n      }\n    })\n  }\n\n  const handleExportText = () => {\n    if (!coverLetter) return\n    exportToText(coverLetter.content, coverLetter.title)\n  }\n\n  const handleCopyToClipboard = async () => {\n    if (!coverLetter) return\n\n    try {\n      await copyToClipboard(coverLetter.content)\n      // You could add a toast notification here\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error)\n    }\n  }\n\n  if (loading || loadingLetter) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user || !coverLetter) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"btn-tertiary\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Dashboard\n            </button>\n            <div>\n              <h1 className=\"text-heading-1 text-gray-900\">{coverLetter.title}</h1>\n              <p className=\"text-gray-600\">\n                Created {new Date(coverLetter.created_at).toLocaleDateString()} • \n                Style: {coverLetter.style}\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            {editing ? (\n              <>\n                <button\n                  onClick={() => {\n                    setEditing(false)\n                    setEditedContent(coverLetter.content)\n                  }}\n                  className=\"btn-secondary\"\n                  disabled={saving}\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSave}\n                  className=\"btn-primary flex items-center space-x-2\"\n                  disabled={saving}\n                >\n                  <Save className=\"h-4 w-4\" />\n                  <span>{saving ? 'Saving...' : 'Save'}</span>\n                </button>\n              </>\n            ) : (\n              <>\n                <button\n                  onClick={() => setEditing(true)}\n                  className=\"btn-secondary\"\n                >\n                  Edit\n                </button>\n                <div className=\"relative\">\n                  <button className=\"btn-primary flex items-center space-x-2\">\n                    <Download className=\"h-4 w-4\" />\n                    <span>Export</span>\n                  </button>\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50 hidden group-hover:block\">\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                    >\n                      Export as PDF\n                    </button>\n                    <button\n                      onClick={handleExportText}\n                      className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                    >\n                      Export as Text\n                    </button>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                    >\n                      Copy to Clipboard\n                    </button>\n                  </div>\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Cover Letter Content */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-heading-2 text-gray-900\">Cover Letter</h2>\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={handleExportPDF}\n                    className=\"btn-tertiary\"\n                    title=\"Export as PDF\"\n                  >\n                    <Download className=\"h-4 w-4\" />\n                  </button>\n                  <button\n                    onClick={handleCopyToClipboard}\n                    className=\"btn-tertiary\"\n                    title=\"Copy to clipboard\"\n                  >\n                    <Copy className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n              \n              {editing ? (\n                <textarea\n                  value={editedContent}\n                  onChange={(e) => setEditedContent(e.target.value)}\n                  className=\"input-field h-96 font-serif\"\n                  placeholder=\"Edit your cover letter content...\"\n                />\n              ) : (\n                <div className=\"prose max-w-none\">\n                  <div className=\"whitespace-pre-wrap font-serif text-gray-900 leading-relaxed\">\n                    {coverLetter.content}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Details */}\n            <div className=\"card\">\n              <h3 className=\"text-heading-3 text-gray-900 mb-4\">Details</h3>\n              <div className=\"space-y-3\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">Status</label>\n                  <p className={`text-sm mt-1 px-2 py-1 rounded-full inline-block ${\n                    coverLetter.status === 'completed' \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    {coverLetter.status}\n                  </p>\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">Style</label>\n                  <p className=\"text-sm text-gray-900 mt-1 capitalize\">{coverLetter.style}</p>\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">Created</label>\n                  <p className=\"text-sm text-gray-900 mt-1\">\n                    {new Date(coverLetter.created_at).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric',\n                      hour: '2-digit',\n                      minute: '2-digit'\n                    })}\n                  </p>\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">Last Updated</label>\n                  <p className=\"text-sm text-gray-900 mt-1\">\n                    {new Date(coverLetter.updated_at).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric',\n                      hour: '2-digit',\n                      minute: '2-digit'\n                    })}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Job Description */}\n            {coverLetter.job_description && (\n              <div className=\"card\">\n                <h3 className=\"text-heading-3 text-gray-900 mb-4\">Job Description</h3>\n                <div className=\"text-sm text-gray-600 max-h-32 overflow-y-auto\">\n                  {coverLetter.job_description}\n                </div>\n              </div>\n            )}\n\n            {/* Quick Actions */}\n            <div className=\"card\">\n              <h3 className=\"text-heading-3 text-gray-900 mb-4\">Quick Actions</h3>\n              <div className=\"space-y-2\">\n                <button\n                  onClick={() => router.push('/create')}\n                  className=\"btn-secondary w-full justify-center\"\n                >\n                  <FileText className=\"h-4 w-4 mr-2\" />\n                  Create New Cover Letter\n                </button>\n                <button\n                  onClick={handleExportPDF}\n                  className=\"btn-secondary w-full justify-center\"\n                >\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Download PDF\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,OAAO,EAAE,EAAE;YACrB;QACF;IACF,GAAG;QAAC;QAAM,OAAO,EAAE;KAAC;IAEpB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,OAAO,EAAE,EAClB,EAAE,CAAC,WAAW,MAAM,IACpB,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,eAAe;YACf,iBAAiB,KAAK,OAAO;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,aAAa;QAElB,UAAU;QAEV,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,CAAC;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,YAAY,EAAE;YAE1B,IAAI,OAAO;gBACT,MAAM;YACR;YAEA,eAAe;gBAAE,GAAG,WAAW;gBAAE,SAAS;YAAc;YACxD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa;QAElB,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;YACV,OAAO,YAAY,KAAK;YACxB,SAAS,YAAY,OAAO;YAC5B,UAAU;gBACR,MAAM,MAAM;gBACZ,OAAO,MAAM;YACf;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa;QAClB,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,YAAY,OAAO,EAAE,YAAY,KAAK;IACrD;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,aAAa;QAElB,IAAI;YACF,MAAM,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,OAAO;QACzC,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,IAAI,WAAW,eAAe;QAC5B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,QAAQ,CAAC,aAAa;QACzB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgC,YAAY,KAAK;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;;oDAAgB;oDAClB,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;oDAAG;oDACvD,YAAY,KAAK;;;;;;;;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;0CACZ,wBACC;;sDACE,8OAAC;4CACC,SAAS;gDACP,WAAW;gDACX,iBAAiB,YAAY,OAAO;4CACtC;4CACA,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;4CACV,UAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAM,SAAS,cAAc;;;;;;;;;;;;;iEAIlC;;sDACE,8OAAC;4CACC,SAAS,IAAM,WAAW;4CAC1B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS;4DACT,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DACC,SAAS;4DACT,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DACC,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAKrB,wBACC,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;4CACV,aAAY;;;;;iEAGd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;0CAQ9B,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,YAAY,MAAM,KAAK,cACnB,gCACA,iCACJ;0EACC,YAAY,MAAM;;;;;;;;;;;;kEAGvB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAAyC,YAAY,KAAK;;;;;;;;;;;;kEAEzE,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB,CAAC,SAAS;oEAC5D,MAAM;oEACN,OAAO;oEACP,KAAK;oEACL,MAAM;oEACN,QAAQ;gEACV;;;;;;;;;;;;kEAGJ,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB,CAAC,SAAS;oEAC5D,MAAM;oEACN,OAAO;oEACP,KAAK;oEACL,MAAM;oEACN,QAAQ;gEACV;;;;;;;;;;;;;;;;;;;;;;;;oCAOP,YAAY,eAAe,kBAC1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DACZ,YAAY,eAAe;;;;;;;;;;;;kDAMlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,OAAO,IAAI,CAAC;wDAC3B,WAAU;;0EAEV,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD", "debugId": null}}]}