#!/usr/bin/env node

/**
 * Database Setup Script
 * 
 * This script helps set up the database by providing instructions
 * and checking if the setup was successful.
 */

const fs = require('fs')
const path = require('path')
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl) {
  console.error('❌ Missing NEXT_PUBLIC_SUPABASE_URL in .env.local')
  process.exit(1)
}

if (!supabaseServiceKey || supabaseServiceKey === 'your_supabase_service_role_key_here') {
  console.error('❌ Missing or invalid SUPABASE_SERVICE_ROLE_KEY in .env.local')
  console.error('Please add your Supabase service role key to .env.local')
  console.error('You can find it in your Supabase project settings under API keys')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupDatabase() {
  console.log('🚀 Setting up CoverCraft AI database...\n')
  
  // Read the schema file
  const schemaPath = path.join(__dirname, '..', 'supabase-schema.sql')
  
  if (!fs.existsSync(schemaPath)) {
    console.error('❌ Schema file not found:', schemaPath)
    console.error('Please make sure supabase-schema.sql exists in the project root')
    process.exit(1)
  }

  console.log('📄 Found schema file:', schemaPath)
  
  // For security reasons, we won't execute the SQL directly from Node.js
  // Instead, we'll provide instructions
  console.log('\n📋 Database Setup Instructions:')
  console.log('1. Open your Supabase project dashboard')
  console.log('2. Go to the SQL Editor')
  console.log('3. Copy and paste the contents of supabase-schema.sql')
  console.log('4. Run the SQL script')
  console.log('5. Run this script again to verify the setup')
  
  console.log('\n🔗 Quick links:')
  console.log(`   Supabase Dashboard: ${supabaseUrl.replace('/rest/v1', '')}/project/_/sql`)
  console.log(`   Schema file: ${schemaPath}`)
  
  // Test if tables already exist
  console.log('\n🔍 Checking current database state...')
  
  try {
    const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true })
    if (!error) {
      console.log('✅ Database appears to be already set up!')
      console.log('   The "users" table exists and is accessible')
      
      // Run verification
      console.log('\n🔍 Running verification...')
      const { spawn } = require('child_process')
      const verify = spawn('node', ['scripts/verify-database.js'], { stdio: 'inherit' })
      
      verify.on('close', (code) => {
        if (code === 0) {
          console.log('\n🎉 Database setup verification completed!')
        } else {
          console.log('\n⚠️  Verification completed with warnings. Check the output above.')
        }
      })
      
      return
    }
    
    if (error.code === '42P01') {
      console.log('❌ Tables do not exist yet')
      console.log('   Please follow the setup instructions above')
    } else {
      console.error('❌ Database error:', error.message)
    }
  } catch (error) {
    console.error('❌ Connection error:', error.message)
    console.log('   Please check your Supabase configuration')
  }
}

setupDatabase().catch(console.error)
