import { NextRequest, NextResponse } from 'next/server'
import { generateCoverLetter } from '@/lib/openai'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { resumeContent, jobDescription, style, userId, userTier } = await request.json()

    // Validate required fields
    if (!resumeContent || !jobDescription || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check user's subscription and credits
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('subscription_tier, credits_remaining')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if free user has credits
    if (user.subscription_tier === 'free' && user.credits_remaining <= 0) {
      return NextResponse.json(
        { error: 'No credits remaining. Please upgrade your subscription.' },
        { status: 403 }
      )
    }

    // Generate cover letter
    const coverLetterContent = await generateCoverLetter({
      resumeContent,
      jobDescription,
      style: style || 'formal',
      userTier: userTier || user.subscription_tier
    })

    // Update user credits if free tier
    if (user.subscription_tier === 'free') {
      const { error: updateError } = await supabase
        .from('users')
        .update({ credits_remaining: user.credits_remaining - 1 })
        .eq('id', userId)

      if (updateError) {
        console.error('Error updating credits:', updateError)
      }

      // Log usage
      await supabase
        .from('user_usage')
        .insert([
          {
            user_id: userId,
            action: 'cover_letter_generated',
            credits_used: 1
          }
        ])
    }

    return NextResponse.json({
      content: coverLetterContent,
      creditsRemaining: user.subscription_tier === 'free' ? user.credits_remaining - 1 : null
    })

  } catch (error: any) {
    console.error('Error generating cover letter:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to generate cover letter' },
      { status: 500 }
    )
  }
}
