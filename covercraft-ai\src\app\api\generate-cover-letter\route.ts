import { NextRequest, NextResponse } from 'next/server'
import { generateCoverLetter } from '@/lib/openai'
import { createClient } from '@supabase/supabase-js'

// Create a server-side Supabase client with service role key to bypass RLS
// If service role key is not available, we'll handle the error gracefully
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    const { resumeContent, jobDescription, style, userId, userTier, resumeId } = await request.json()

    // Validate required fields
    if (!resumeContent || !jobDescription || !userId || !resumeId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Check user's subscription and credits
    console.log('Looking up user with ID:', userId)
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('subscription_tier, credits_remaining')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('User lookup error:', {
        error: userError,
        code: userError.code,
        message: userError.message,
        details: userError.details,
        hint: userError.hint,
        userId
      })
      return NextResponse.json(
        { error: `User lookup failed: ${userError.message}` },
        { status: 404 }
      )
    }

    if (!user) {
      console.error('No user found with ID:', userId)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    console.log('User found:', { id: userId, tier: user.subscription_tier, credits: user.credits_remaining })

    // Check if free user has credits
    if (user.subscription_tier === 'free' && user.credits_remaining <= 0) {
      return NextResponse.json(
        { error: 'No credits remaining. Please upgrade your subscription.' },
        { status: 403 }
      )
    }

    // Generate cover letter
    console.log('Generating cover letter...')
    const coverLetterContent = await generateCoverLetter({
      resumeContent,
      jobDescription,
      style: style || 'formal',
      userTier: userTier || user.subscription_tier
    })

    console.log('Cover letter generated successfully')

    // Save the cover letter to the database
    const { data: coverLetter, error: saveError } = await supabaseAdmin
      .from('cover_letters')
      .insert([
        {
          user_id: userId,
          resume_id: resumeId,
          title: `Cover Letter - ${new Date().toLocaleDateString()}`,
          content: coverLetterContent,
          job_description: jobDescription,
          style: style || 'formal',
          status: 'completed'
        }
      ])
      .select()
      .single()

    if (saveError) {
      console.error('Error saving cover letter:', saveError)
      return NextResponse.json(
        { error: 'Failed to save cover letter' },
        { status: 500 }
      )
    }

    console.log('Cover letter saved with ID:', coverLetter.id)

    // Update user credits if free tier
    if (user.subscription_tier === 'free') {
      const { error: updateError } = await supabaseAdmin
        .from('users')
        .update({ credits_remaining: user.credits_remaining - 1 })
        .eq('id', userId)

      if (updateError) {
        console.error('Error updating credits:', updateError)
      }

      // Log usage
      await supabaseAdmin
        .from('user_usage')
        .insert([
          {
            user_id: userId,
            action: 'cover_letter_generated',
            credits_used: 1
          }
        ])
    }

    return NextResponse.json({
      content: coverLetterContent,
      coverLetterId: coverLetter.id,
      creditsRemaining: user.subscription_tier === 'free' ? user.credits_remaining - 1 : null
    })

  } catch (error: any) {
    console.error('Error generating cover letter:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to generate cover letter' },
      { status: 500 }
    )
  }
}
