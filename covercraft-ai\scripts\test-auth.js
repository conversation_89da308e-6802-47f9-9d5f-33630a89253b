#!/usr/bin/env node

/**
 * Authentication Test Script
 * 
 * This script tests the authentication flow and user profile creation
 * to help debug the "Error fetching user profile" issue.
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuth() {
  console.log('🧪 Testing authentication flow...\n')
  
  // Test 1: Check current session
  console.log('1. Checking current session...')
  try {
    const { data: { session }, error } = await supabase.auth.getSession()
    if (error) {
      console.error('❌ Session check failed:', error.message)
      return
    }
    
    if (session) {
      console.log('✅ Active session found')
      console.log(`   User ID: ${session.user.id}`)
      console.log(`   Email: ${session.user.email}`)
      
      // Test fetching user profile
      await testFetchUserProfile(session.user.id)
    } else {
      console.log('ℹ️  No active session')
      console.log('   To test with a real user, sign in through the web app first')
    }
  } catch (error) {
    console.error('❌ Session check error:', error.message)
  }
  
  // Test 2: Test user profile query structure
  console.log('\n2. Testing user profile query...')
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('❌ Query failed:', error.message)
      console.log(`   Error code: ${error.code}`)
      if (error.code === '42501') {
        console.log('   → This is expected if RLS is properly configured')
      }
    } else {
      console.log('✅ Query successful')
      console.log(`   Found ${data.length} records`)
      if (data.length > 0) {
        console.log('   Sample record structure:', Object.keys(data[0]))
      }
    }
  } catch (error) {
    console.error('❌ Query error:', error.message)
  }
  
  // Test 3: Test table structure
  console.log('\n3. Testing table structure...')
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id, email, full_name, subscription_tier, credits_remaining')
      .limit(0)
    
    if (error) {
      console.log('❌ Structure test failed:', error.message)
      if (error.message.includes('column') && error.message.includes('does not exist')) {
        console.log('   → Some columns might be missing from the users table')
      }
    } else {
      console.log('✅ Table structure looks correct')
    }
  } catch (error) {
    console.error('❌ Structure test error:', error.message)
  }
}

async function testFetchUserProfile(userId) {
  console.log('\n   Testing user profile fetch...')
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.log('   ❌ Profile fetch failed:', error.message)
      console.log(`      Error code: ${error.code}`)
      console.log(`      Details: ${error.details}`)
      console.log(`      Hint: ${error.hint}`)
      
      if (error.code === 'PGRST116') {
        console.log('   → User profile does not exist in users table')
        console.log('   → This would trigger automatic profile creation in the app')
      }
    } else {
      console.log('   ✅ Profile fetch successful')
      console.log(`      User: ${data.email}`)
      console.log(`      Tier: ${data.subscription_tier}`)
      console.log(`      Credits: ${data.credits_remaining}`)
    }
  } catch (error) {
    console.error('   ❌ Profile fetch error:', error.message)
  }
}

testAuth().catch(console.error)
