-- CoverCraft AI Database Schema
-- Run this in your Supabase SQL editor to set up the database

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- <PERSON>reate custom types
CREATE TYPE subscription_tier AS ENUM ('free', 'standard', 'premium');
CREATE TYPE cover_letter_style AS ENUM ('formal', 'conversational', 'creative');
CREATE TYPE cover_letter_status AS ENUM ('draft', 'completed');
CREATE TYPE usage_action AS ENUM ('cover_letter_generated', 'credit_used');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier subscription_tier DEFAULT 'free',
    credits_remaining INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Resumes table
CREATE TABLE public.resumes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    file_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cover letters table
CREATE TABLE public.cover_letters (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    resume_id UUID REFERENCES public.resumes(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    job_description TEXT,
    style cover_letter_style DEFAULT 'formal',
    status cover_letter_status DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Templates table
CREATE TABLE public.templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    category TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User usage tracking table
CREATE TABLE public.user_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    action usage_action NOT NULL,
    credits_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_resumes_user_id ON public.resumes(user_id);
CREATE INDEX idx_cover_letters_user_id ON public.cover_letters(user_id);
CREATE INDEX idx_cover_letters_resume_id ON public.cover_letters(resume_id);
CREATE INDEX idx_user_usage_user_id ON public.user_usage(user_id);
CREATE INDEX idx_templates_category ON public.templates(category);
CREATE INDEX idx_templates_is_premium ON public.templates(is_premium);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_resumes_updated_at BEFORE UPDATE ON public.resumes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cover_letters_updated_at BEFORE UPDATE ON public.cover_letters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resumes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cover_letters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_usage ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Resumes policies
CREATE POLICY "Users can view own resumes" ON public.resumes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own resumes" ON public.resumes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own resumes" ON public.resumes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own resumes" ON public.resumes
    FOR DELETE USING (auth.uid() = user_id);

-- Cover letters policies
CREATE POLICY "Users can view own cover letters" ON public.cover_letters
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own cover letters" ON public.cover_letters
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own cover letters" ON public.cover_letters
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own cover letters" ON public.cover_letters
    FOR DELETE USING (auth.uid() = user_id);

-- Templates policies (read-only for users)
CREATE POLICY "Anyone can view templates" ON public.templates
    FOR SELECT USING (true);

-- User usage policies
CREATE POLICY "Users can view own usage" ON public.user_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage" ON public.user_usage
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert some sample templates
INSERT INTO public.templates (title, description, content, category, is_premium) VALUES
(
    'Software Engineer Cover Letter',
    'Professional template for software engineering positions',
    'Dear Hiring Manager,

I am writing to express my strong interest in the Software Engineer position at [Company Name]. With my background in [relevant technologies] and passion for creating innovative solutions, I am excited about the opportunity to contribute to your development team.

In my previous role at [Previous Company], I successfully [specific achievement]. My experience with [relevant skills] has prepared me to tackle the challenges outlined in your job description, particularly [specific requirement from job posting].

I am particularly drawn to [Company Name] because of [specific reason related to company]. Your commitment to [company value/mission] aligns perfectly with my professional values and career goals.

I would welcome the opportunity to discuss how my skills and enthusiasm can contribute to your team''s success. Thank you for considering my application.

Sincerely,
[Your Name]',
    'Technology',
    false
),
(
    'Marketing Manager Cover Letter',
    'Template for marketing and communications roles',
    'Dear [Hiring Manager Name],

I am excited to apply for the Marketing Manager position at [Company Name]. With [X years] of experience in digital marketing and a proven track record of driving brand growth, I am confident I can help [Company Name] achieve its marketing objectives.

In my current role at [Current Company], I have successfully [specific marketing achievement]. My expertise in [marketing channels/tools] has enabled me to [specific result]. I am particularly skilled at [relevant marketing skill] and have experience managing campaigns that [specific outcome].

What attracts me most to [Company Name] is [specific reason]. I admire your recent [campaign/initiative] and believe my background in [relevant area] would allow me to contribute meaningfully to your continued success.

I am eager to bring my creative problem-solving skills and data-driven approach to your marketing team. Thank you for your time and consideration.

Best regards,
[Your Name]',
    'Marketing',
    false
),
(
    'Executive Level Cover Letter',
    'Premium template for senior executive positions',
    'Dear Members of the Executive Search Committee,

I am writing to express my interest in the [Executive Position] role at [Company Name]. As a seasoned executive with [X years] of leadership experience in [industry], I am excited about the opportunity to drive strategic growth and operational excellence at your organization.

Throughout my career, I have consistently delivered transformational results. At [Previous Company], I [major achievement with quantifiable results]. My leadership philosophy centers on [leadership approach], which has enabled me to build high-performing teams and navigate complex business challenges.

I am particularly drawn to [Company Name] because of [specific strategic reason]. Your recent [business development/expansion/initiative] demonstrates the kind of forward-thinking approach that aligns with my experience in [relevant area]. I believe my expertise in [specific executive skill] would be invaluable as you [company goal/challenge].

I would welcome the opportunity to discuss how my strategic vision and proven track record can contribute to [Company Name]''s continued success and growth.

Respectfully,
[Your Name]',
    'Executive',
    true
);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
