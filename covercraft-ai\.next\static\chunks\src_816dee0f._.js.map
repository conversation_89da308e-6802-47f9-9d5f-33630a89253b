{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogOut, User, CreditCard } from 'lucide-react'\nimport { useState } from 'react'\n\nexport default function Header() {\n  const { user, signOut } = useAuth()\n  const [showDropdown, setShowDropdown] = useState(false)\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  const getTierBadgeColor = (tier: string) => {\n    switch (tier) {\n      case 'premium':\n        return 'bg-purple-100 text-purple-800'\n      case 'standard':\n        return 'bg-blue-100 text-blue-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <header className=\"bg-white border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <h1 className=\"text-2xl font-bold text-blue-600\">CoverCraft AI</h1>\n          </div>\n\n          {/* User Menu */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Credits Display */}\n            {user && (\n              <div className=\"flex items-center space-x-2\">\n                <CreditCard className=\"h-5 w-5 text-gray-400\" />\n                <span className=\"text-sm text-gray-600\">\n                  {user.subscription_tier === 'free' \n                    ? `${user.credits_remaining} credits left`\n                    : 'Unlimited'\n                  }\n                </span>\n                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTierBadgeColor(user.subscription_tier)}`}>\n                  {user.subscription_tier.charAt(0).toUpperCase() + user.subscription_tier.slice(1)}\n                </span>\n              </div>\n            )}\n\n            {/* User Dropdown */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setShowDropdown(!showDropdown)}\n                className=\"flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none\"\n              >\n                <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <User className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <span className=\"text-sm font-medium\">{user?.full_name || user?.email}</span>\n              </button>\n\n              {showDropdown && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\">\n                  <div className=\"px-4 py-2 border-b border-gray-100\">\n                    <p className=\"text-sm font-medium text-gray-900\">{user?.full_name}</p>\n                    <p className=\"text-sm text-gray-500\">{user?.email}</p>\n                  </div>\n                  \n                  <button\n                    onClick={() => {\n                      setShowDropdown(false)\n                      // Navigate to profile settings\n                    }}\n                    className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    <span>Profile Settings</span>\n                  </button>\n                  \n                  <button\n                    onClick={() => {\n                      setShowDropdown(false)\n                      // Navigate to subscription\n                    }}\n                    className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                  >\n                    <CreditCard className=\"h-4 w-4\" />\n                    <span>Subscription</span>\n                  </button>\n                  \n                  <hr className=\"my-1\" />\n                  \n                  <button\n                    onClick={handleSignOut}\n                    className=\"w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 flex items-center space-x-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span>Sign out</span>\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;;;;;;kCAInD,6LAAC;wBAAI,WAAU;;4BAEZ,sBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDACb,KAAK,iBAAiB,KAAK,SACxB,GAAG,KAAK,iBAAiB,CAAC,aAAa,CAAC,GACxC;;;;;;kDAGN,6LAAC;wCAAK,WAAW,CAAC,2CAA2C,EAAE,kBAAkB,KAAK,iBAAiB,GAAG;kDACvG,KAAK,iBAAiB,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC;;;;;;;;;;;;0CAMrF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAK,WAAU;0DAAuB,MAAM,aAAa,MAAM;;;;;;;;;;;;oCAGjE,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,MAAM;;;;;;kEACxD,6LAAC;wDAAE,WAAU;kEAAyB,MAAM;;;;;;;;;;;;0DAG9C,6LAAC;gDACC,SAAS;oDACP,gBAAgB;gDAChB,+BAA+B;gDACjC;gDACA,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;0DAGR,6LAAC;gDACC,SAAS;oDACP,gBAAgB;gDAChB,2BAA2B;gDAC7B;gDACA,WAAU;;kEAEV,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;kEAAK;;;;;;;;;;;;0DAGR,6LAAC;gDAAG,WAAU;;;;;;0DAEd,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;GA5GwB;;QACI,kIAAA,CAAA,UAAO;;;KADX", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai'\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n})\n\nexport interface CoverLetterRequest {\n  resumeContent: string\n  jobDescription: string\n  style: 'formal' | 'conversational' | 'creative'\n  userTier: 'free' | 'standard' | 'premium'\n}\n\nexport async function generateCoverLetter({\n  resumeContent,\n  jobDescription,\n  style,\n  userTier\n}: CoverLetterRequest): Promise<string> {\n  const model = userTier === 'free' ? 'gpt-3.5-turbo' : 'gpt-4'\n  \n  const stylePrompts = {\n    formal: 'Write in a professional, formal tone suitable for corporate environments.',\n    conversational: 'Write in a friendly, conversational tone while maintaining professionalism.',\n    creative: 'Write in an engaging, creative tone that showcases personality while remaining professional.'\n  }\n\n  const systemPrompt = `You are an expert cover letter writer. Create a compelling, personalized cover letter based on the provided resume and job description. \n\nGuidelines:\n- ${stylePrompts[style]}\n- Keep it concise (3-4 paragraphs)\n- Highlight relevant skills and experiences from the resume\n- Show enthusiasm for the specific role and company\n- Include a strong opening and closing\n- Make it ATS-friendly\n- Avoid generic phrases and clichés\n- Ensure the letter flows naturally and tells a story\n\nFormat the response as a complete cover letter without any additional commentary.`\n\n  const userPrompt = `Resume Content:\n${resumeContent}\n\nJob Description:\n${jobDescription}\n\nPlease write a tailored cover letter for this position.`\n\n  try {\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        { role: 'system', content: systemPrompt },\n        { role: 'user', content: userPrompt }\n      ],\n      max_tokens: userTier === 'premium' ? 1000 : 800,\n      temperature: 0.7,\n    })\n\n    return completion.choices[0]?.message?.content || 'Failed to generate cover letter'\n  } catch (error) {\n    console.error('OpenAI API error:', error)\n    throw new Error('Failed to generate cover letter. Please try again.')\n  }\n}\n\nexport async function improveCoverLetterSection(\n  section: string,\n  improvement: string,\n  userTier: 'free' | 'standard' | 'premium'\n): Promise<string> {\n  if (userTier === 'free') {\n    throw new Error('AI editing assistance is only available for paid subscribers')\n  }\n\n  const model = userTier === 'standard' ? 'gpt-4' : 'gpt-4'\n\n  const systemPrompt = `You are an expert writing assistant. Improve the given text section based on the user's request. Maintain the original meaning while enhancing clarity, impact, and professionalism.`\n\n  const userPrompt = `Original text:\n${section}\n\nImprovement request:\n${improvement}\n\nPlease provide the improved version:`\n\n  try {\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        { role: 'system', content: systemPrompt },\n        { role: 'user', content: userPrompt }\n      ],\n      max_tokens: 500,\n      temperature: 0.5,\n    })\n\n    return completion.choices[0]?.message?.content || 'Failed to improve section'\n  } catch (error) {\n    console.error('OpenAI API error:', error)\n    throw new Error('Failed to improve section. Please try again.')\n  }\n}\n"], "names": [], "mappings": ";;;;AAGU;AAHV;;AAEA,MAAM,SAAS,IAAI,mJAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc;AACpC;AASO,eAAe,oBAAoB,EACxC,aAAa,EACb,cAAc,EACd,KAAK,EACL,QAAQ,EACW;IACnB,MAAM,QAAQ,aAAa,SAAS,kBAAkB;IAEtD,MAAM,eAAe;QACnB,QAAQ;QACR,gBAAgB;QAChB,UAAU;IACZ;IAEA,MAAM,eAAe,CAAC;;;EAGtB,EAAE,YAAY,CAAC,MAAM,CAAC;;;;;;;;;iFASyD,CAAC;IAEhF,MAAM,aAAa,CAAC;AACtB,EAAE,cAAc;;;AAGhB,EAAE,eAAe;;uDAEsC,CAAC;IAEtD,IAAI;QACF,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAAa;gBACxC;oBAAE,MAAM;oBAAQ,SAAS;gBAAW;aACrC;YACD,YAAY,aAAa,YAAY,OAAO;YAC5C,aAAa;QACf;QAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,0BACpB,OAAe,EACf,WAAmB,EACnB,QAAyC;IAEzC,IAAI,aAAa,QAAQ;QACvB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,aAAa,aAAa,UAAU;IAElD,MAAM,eAAe,CAAC,oLAAoL,CAAC;IAE3M,MAAM,aAAa,CAAC;AACtB,EAAE,QAAQ;;;AAGV,EAAE,YAAY;;oCAEsB,CAAC;IAEnC,IAAI;QACF,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAAa;gBACxC;oBAAE,MAAM;oBAAQ,SAAS;gBAAW;aACrC;YACD,YAAY;YACZ,aAAa;QACf;QAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/app/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Header from '@/components/layout/Header'\nimport { Upload, FileText, Wand2, ArrowLeft } from 'lucide-react'\nimport { supabase, Resume } from '@/lib/supabase'\nimport { generateCoverLetter } from '@/lib/openai'\n\nexport default function CreatePage() {\n  const { user, loading, updateCredits } = useAuth()\n  const router = useRouter()\n  const [step, setStep] = useState(1)\n  const [resumes, setResumes] = useState<Resume[]>([])\n  const [selectedResume, setSelectedResume] = useState<Resume | null>(null)\n  const [jobDescription, setJobDescription] = useState('')\n  const [style, setStyle] = useState<'formal' | 'conversational' | 'creative'>('formal')\n  const [generating, setGenerating] = useState(false)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth')\n    }\n  }, [user, loading, router])\n\n  useEffect(() => {\n    if (user) {\n      fetchResumes()\n    }\n  }, [user])\n\n  const fetchResumes = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('resumes')\n        .select('*')\n        .eq('user_id', user?.id)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching resumes:', error)\n        return\n      }\n\n      setResumes(data || [])\n    } catch (error) {\n      console.error('Error fetching resumes:', error)\n    }\n  }\n\n  const handleGenerate = async () => {\n    if (!selectedResume || !jobDescription.trim()) {\n      setError('Please select a resume and enter a job description')\n      return\n    }\n\n    if (user?.subscription_tier === 'free' && user.credits_remaining <= 0) {\n      setError('You have no credits remaining. Please upgrade your subscription.')\n      return\n    }\n\n    setGenerating(true)\n    setError('')\n\n    try {\n      const coverLetterContent = await generateCoverLetter({\n        resumeContent: selectedResume.content,\n        jobDescription,\n        style,\n        userTier: user?.subscription_tier || 'free'\n      })\n\n      // Save the cover letter\n      const { data, error } = await supabase\n        .from('cover_letters')\n        .insert([\n          {\n            user_id: user?.id,\n            resume_id: selectedResume.id,\n            title: `Cover Letter - ${new Date().toLocaleDateString()}`,\n            content: coverLetterContent,\n            job_description: jobDescription,\n            style,\n            status: 'completed'\n          }\n        ])\n        .select()\n        .single()\n\n      if (error) {\n        throw error\n      }\n\n      // Update credits for free users\n      if (user?.subscription_tier === 'free') {\n        await updateCredits(user.credits_remaining - 1)\n      }\n\n      // Redirect to the cover letter editor\n      router.push(`/cover-letters/${data.id}`)\n    } catch (error: any) {\n      setError(error.message || 'Failed to generate cover letter')\n    } finally {\n      setGenerating(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center space-x-4 mb-8\">\n          <button\n            onClick={() => router.push('/dashboard')}\n            className=\"btn-tertiary\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Dashboard\n          </button>\n          <div>\n            <h1 className=\"text-heading-1 text-gray-900\">Create Cover Letter</h1>\n            <p className=\"text-gray-600\">Generate a personalized cover letter with AI</p>\n          </div>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"flex items-center space-x-4 mb-8\">\n          <div className={`flex items-center space-x-2 ${step >= 1 ? 'text-blue-600' : 'text-gray-400'}`}>\n            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>\n              1\n            </div>\n            <span className=\"font-medium\">Select Resume</span>\n          </div>\n          <div className=\"flex-1 h-px bg-gray-200\"></div>\n          <div className={`flex items-center space-x-2 ${step >= 2 ? 'text-blue-600' : 'text-gray-400'}`}>\n            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>\n              2\n            </div>\n            <span className=\"font-medium\">Job Details</span>\n          </div>\n          <div className=\"flex-1 h-px bg-gray-200\"></div>\n          <div className={`flex items-center space-x-2 ${step >= 3 ? 'text-blue-600' : 'text-gray-400'}`}>\n            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>\n              3\n            </div>\n            <span className=\"font-medium\">Generate</span>\n          </div>\n        </div>\n\n        {/* Step Content */}\n        <div className=\"card\">\n          {step === 1 && (\n            <div>\n              <h2 className=\"text-heading-2 text-gray-900 mb-4\">Select a Resume</h2>\n              \n              {resumes.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No resumes found</h3>\n                  <p className=\"text-gray-600 mb-4\">Upload a resume to get started with your cover letter.</p>\n                  <button\n                    onClick={() => router.push('/resumes')}\n                    className=\"btn-primary\"\n                  >\n                    Upload Resume\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {resumes.map((resume) => (\n                    <div\n                      key={resume.id}\n                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                        selectedResume?.id === resume.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 hover:border-gray-300'\n                      }`}\n                      onClick={() => setSelectedResume(resume)}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <FileText className=\"h-5 w-5 text-gray-400\" />\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{resume.title}</h4>\n                          <p className=\"text-sm text-gray-500\">\n                            Updated {new Date(resume.updated_at).toLocaleDateString()}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                  \n                  <div className=\"flex justify-between pt-4\">\n                    <button\n                      onClick={() => router.push('/resumes')}\n                      className=\"btn-secondary\"\n                    >\n                      Manage Resumes\n                    </button>\n                    <button\n                      onClick={() => setStep(2)}\n                      disabled={!selectedResume}\n                      className=\"btn-primary\"\n                    >\n                      Continue\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n\n          {step === 2 && (\n            <div>\n              <h2 className=\"text-heading-2 text-gray-900 mb-4\">Job Description & Style</h2>\n              \n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Job Description\n                  </label>\n                  <textarea\n                    value={jobDescription}\n                    onChange={(e) => setJobDescription(e.target.value)}\n                    className=\"input-field h-32\"\n                    placeholder=\"Paste the job description here...\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Writing Style\n                  </label>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n                    {[\n                      { value: 'formal', label: 'Formal', description: 'Professional and traditional' },\n                      { value: 'conversational', label: 'Conversational', description: 'Friendly yet professional' },\n                      { value: 'creative', label: 'Creative', description: 'Engaging and unique' }\n                    ].map((option) => (\n                      <div\n                        key={option.value}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          style === option.value\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                        onClick={() => setStyle(option.value as any)}\n                      >\n                        <h4 className=\"font-medium text-gray-900\">{option.label}</h4>\n                        <p className=\"text-sm text-gray-500\">{option.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <button\n                    onClick={() => setStep(1)}\n                    className=\"btn-secondary\"\n                  >\n                    Back\n                  </button>\n                  <button\n                    onClick={() => setStep(3)}\n                    disabled={!jobDescription.trim()}\n                    className=\"btn-primary\"\n                  >\n                    Continue\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {step === 3 && (\n            <div>\n              <h2 className=\"text-heading-2 text-gray-900 mb-4\">Review & Generate</h2>\n              \n              <div className=\"space-y-4 mb-6\">\n                <div className=\"p-4 bg-gray-50 rounded-lg\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Selected Resume</h4>\n                  <p className=\"text-gray-600\">{selectedResume?.title}</p>\n                </div>\n                \n                <div className=\"p-4 bg-gray-50 rounded-lg\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Writing Style</h4>\n                  <p className=\"text-gray-600 capitalize\">{style}</p>\n                </div>\n                \n                <div className=\"p-4 bg-gray-50 rounded-lg\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Job Description</h4>\n                  <p className=\"text-gray-600 text-sm line-clamp-3\">{jobDescription}</p>\n                </div>\n              </div>\n\n              {error && (\n                <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4\">\n                  {error}\n                </div>\n              )}\n\n              <div className=\"flex justify-between\">\n                <button\n                  onClick={() => setStep(2)}\n                  className=\"btn-secondary\"\n                  disabled={generating}\n                >\n                  Back\n                </button>\n                <button\n                  onClick={handleGenerate}\n                  disabled={generating}\n                  className=\"btn-primary flex items-center space-x-2\"\n                >\n                  <Wand2 className=\"h-4 w-4\" />\n                  <span>{generating ? 'Generating...' : 'Generate Cover Letter'}</span>\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;+BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;+BAAG;QAAC;KAAK;IAET,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,MAAM,IACpB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC;YACF;YAEA,WAAW,QAAQ,EAAE;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,kBAAkB,CAAC,eAAe,IAAI,IAAI;YAC7C,SAAS;YACT;QACF;QAEA,IAAI,MAAM,sBAAsB,UAAU,KAAK,iBAAiB,IAAI,GAAG;YACrE,SAAS;YACT;QACF;QAEA,cAAc;QACd,SAAS;QAET,IAAI;YACF,MAAM,qBAAqB,MAAM,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE;gBACnD,eAAe,eAAe,OAAO;gBACrC;gBACA;gBACA,UAAU,MAAM,qBAAqB;YACvC;YAEA,wBAAwB;YACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;gBACN;oBACE,SAAS,MAAM;oBACf,WAAW,eAAe,EAAE;oBAC5B,OAAO,CAAC,eAAe,EAAE,IAAI,OAAO,kBAAkB,IAAI;oBAC1D,SAAS;oBACT,iBAAiB;oBACjB;oBACA,QAAQ;gBACV;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,MAAM;YACR;YAEA,gCAAgC;YAChC,IAAI,MAAM,sBAAsB,QAAQ;gBACtC,MAAM,cAAc,KAAK,iBAAiB,GAAG;YAC/C;YAEA,sCAAsC;YACtC,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE;QACzC,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAKjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,IAAI,kBAAkB,iBAAiB;;kDAC5F,6LAAC;wCAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,IAAI,2BAA2B,eAAe;kDAAE;;;;;;kDAGjI,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEhC,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,IAAI,kBAAkB,iBAAiB;;kDAC5F,6LAAC;wCAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,IAAI,2BAA2B,eAAe;kDAAE;;;;;;kDAGjI,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEhC,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,IAAI,kBAAkB,iBAAiB;;kDAC5F,6LAAC;wCAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,IAAI,2BAA2B,eAAe;kDAAE;;;;;;kDAGjI,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;kCAKlC,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,mBACR,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;oCAEjD,QAAQ,MAAM,KAAK,kBAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;6DAKH,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oDAEC,WAAW,CAAC,uDAAuD,EACjE,gBAAgB,OAAO,OAAO,EAAE,GAC5B,+BACA,yCACJ;oDACF,SAAS,IAAM,kBAAkB;8DAEjC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA6B,OAAO,KAAK;;;;;;kFACvD,6LAAC;wEAAE,WAAU;;4EAAwB;4EAC1B,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;mDAbxD,OAAO,EAAE;;;;;0DAoBlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,OAAO,IAAI,CAAC;wDAC3B,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,QAAQ;wDACvB,UAAU,CAAC;wDACX,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;4BASV,SAAS,mBACR,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDAAI,WAAU;kEACZ;4DACC;gEAAE,OAAO;gEAAU,OAAO;gEAAU,aAAa;4DAA+B;4DAChF;gEAAE,OAAO;gEAAkB,OAAO;gEAAkB,aAAa;4DAA4B;4DAC7F;gEAAE,OAAO;gEAAY,OAAO;gEAAY,aAAa;4DAAsB;yDAC5E,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC;gEAEC,WAAW,CAAC,uDAAuD,EACjE,UAAU,OAAO,KAAK,GAClB,+BACA,yCACJ;gEACF,SAAS,IAAM,SAAS,OAAO,KAAK;;kFAEpC,6LAAC;wEAAG,WAAU;kFAA6B,OAAO,KAAK;;;;;;kFACvD,6LAAC;wEAAE,WAAU;kFAAyB,OAAO,WAAW;;;;;;;+DATnD,OAAO,KAAK;;;;;;;;;;;;;;;;0DAezB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,QAAQ;wDACvB,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,QAAQ;wDACvB,UAAU,CAAC,eAAe,IAAI;wDAC9B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;4BAQR,SAAS,mBACR,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAAiB,gBAAgB;;;;;;;;;;;;0DAGhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAA4B;;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAAsC;;;;;;;;;;;;;;;;;;oCAItD,uBACC,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,QAAQ;gDACvB,WAAU;gDACV,UAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;;kEAEV,6LAAC,kNAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAM,aAAa,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GA7UwB;;QACmB,kIAAA,CAAA,UAAO;QACjC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}