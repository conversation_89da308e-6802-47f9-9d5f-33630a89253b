#!/usr/bin/env node

/**
 * Database Verification Script
 * 
 * This script checks if the required database tables exist in Supabase
 * and provides helpful information for debugging database issues.
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Please check your .env.local file contains:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- NEXT_PUBLIC_SUPABASE_ANON_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function verifyDatabase() {
  console.log('🔍 Verifying database setup...\n')
  
  // Test connection
  console.log('1. Testing Supabase connection...')
  try {
    const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true })
    if (error) {
      console.error('❌ Connection failed:', error.message)
      if (error.code === '42P01') {
        console.error('   → The "users" table does not exist')
        console.error('   → Please run the SQL schema in your Supabase SQL editor')
        console.error('   → Schema file: supabase-schema.sql')
      }
      return false
    }
    console.log('✅ Connection successful')
  } catch (error) {
    console.error('❌ Connection error:', error.message)
    return false
  }

  // Check required tables
  console.log('\n2. Checking required tables...')
  const tables = ['users', 'resumes', 'cover_letters', 'templates', 'user_usage']
  
  for (const table of tables) {
    try {
      const { error } = await supabase.from(table).select('count', { count: 'exact', head: true })
      if (error) {
        console.error(`❌ Table "${table}" - ${error.message}`)
      } else {
        console.log(`✅ Table "${table}" exists`)
      }
    } catch (error) {
      console.error(`❌ Table "${table}" - ${error.message}`)
    }
  }

  // Check RLS policies
  console.log('\n3. Testing Row Level Security...')
  try {
    // This should fail if RLS is properly configured and user is not authenticated
    const { data, error } = await supabase.from('users').select('*').limit(1)
    if (error && error.code === '42501') {
      console.log('✅ RLS is properly configured (access denied for unauthenticated user)')
    } else if (error) {
      console.error('❌ RLS test failed:', error.message)
    } else {
      console.log('⚠️  RLS might not be properly configured (data returned without authentication)')
    }
  } catch (error) {
    console.error('❌ RLS test error:', error.message)
  }

  // Check auth configuration
  console.log('\n4. Testing authentication...')
  try {
    const { data, error } = await supabase.auth.getSession()
    if (error) {
      console.error('❌ Auth test failed:', error.message)
    } else {
      console.log('✅ Auth service is accessible')
      if (data.session) {
        console.log('✅ User is currently authenticated')
      } else {
        console.log('ℹ️  No active session (user not logged in)')
      }
    }
  } catch (error) {
    console.error('❌ Auth test error:', error.message)
  }

  console.log('\n📋 Summary:')
  console.log('If you see errors above, please:')
  console.log('1. Make sure you have run the SQL schema in your Supabase SQL editor')
  console.log('2. Check that your Supabase URL and keys are correct')
  console.log('3. Verify that Row Level Security is enabled on all tables')
  console.log('4. Ensure the database policies are properly configured')
  console.log('\nFor more help, see: https://supabase.com/docs/guides/database')
}

verifyDatabase().catch(console.error)
