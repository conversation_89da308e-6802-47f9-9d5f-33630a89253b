import jsPDF from 'jspdf'

export interface ExportOptions {
  title: string
  content: string
  userInfo?: {
    name?: string
    email?: string
    phone?: string
    address?: string
  }
}

export function exportToPDF({ title, content, userInfo }: ExportOptions) {
  const doc = new jsPDF()
  
  // Set up fonts and styling
  const pageWidth = doc.internal.pageSize.getWidth()
  const pageHeight = doc.internal.pageSize.getHeight()
  const margin = 20
  const maxWidth = pageWidth - (margin * 2)
  
  let yPosition = margin

  // Add user info header if provided
  if (userInfo) {
    doc.setFontSize(12)
    doc.setFont('helvetica', 'normal')
    
    if (userInfo.name) {
      doc.text(userInfo.name, margin, yPosition)
      yPosition += 6
    }
    
    if (userInfo.email) {
      doc.text(userInfo.email, margin, yPosition)
      yPosition += 6
    }
    
    if (userInfo.phone) {
      doc.text(userInfo.phone, margin, yPosition)
      yPosition += 6
    }
    
    if (userInfo.address) {
      doc.text(userInfo.address, margin, yPosition)
      yPosition += 6
    }
    
    yPosition += 10 // Extra space after header
  }

  // Add date
  const today = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
  
  doc.setFontSize(11)
  doc.text(today, margin, yPosition)
  yPosition += 15

  // Process and add content
  doc.setFontSize(11)
  doc.setFont('helvetica', 'normal')
  
  // Split content into paragraphs
  const paragraphs = content.split('\n\n').filter(p => p.trim())
  
  paragraphs.forEach((paragraph, index) => {
    // Clean up the paragraph
    const cleanParagraph = paragraph.trim()
    
    if (cleanParagraph) {
      // Split text to fit within margins
      const lines = doc.splitTextToSize(cleanParagraph, maxWidth)
      
      // Check if we need a new page
      if (yPosition + (lines.length * 5) > pageHeight - margin) {
        doc.addPage()
        yPosition = margin
      }
      
      // Add the paragraph
      lines.forEach((line: string) => {
        doc.text(line, margin, yPosition)
        yPosition += 5
      })
      
      // Add space between paragraphs
      yPosition += 5
    }
  })

  // Save the PDF
  const fileName = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`
  doc.save(fileName)
}

export function exportToText(content: string, title: string) {
  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

export function copyToClipboard(content: string) {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(content)
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = content
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    return new Promise<void>((resolve, reject) => {
      if (document.execCommand('copy')) {
        resolve()
      } else {
        reject(new Error('Failed to copy to clipboard'))
      }
      document.body.removeChild(textArea)
    })
  }
}
