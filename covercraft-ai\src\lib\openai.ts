import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface CoverLetterRequest {
  resumeContent: string
  jobDescription: string
  style: 'formal' | 'conversational' | 'creative'
  userTier: 'free' | 'standard' | 'premium'
}

export async function generateCoverLetter({
  resumeContent,
  jobDescription,
  style,
  userTier
}: CoverLetterRequest): Promise<string> {
  const model = userTier === 'free' ? 'gpt-3.5-turbo' : 'gpt-4'
  
  const stylePrompts = {
    formal: 'Write in a professional, formal tone suitable for corporate environments.',
    conversational: 'Write in a friendly, conversational tone while maintaining professionalism.',
    creative: 'Write in an engaging, creative tone that showcases personality while remaining professional.'
  }

  const systemPrompt = `You are an expert cover letter writer. Create a compelling, personalized cover letter based on the provided resume and job description. 

Guidelines:
- ${stylePrompts[style]}
- Keep it concise (3-4 paragraphs)
- Highlight relevant skills and experiences from the resume
- Show enthusiasm for the specific role and company
- Include a strong opening and closing
- Make it ATS-friendly
- Avoid generic phrases and clichés
- Ensure the letter flows naturally and tells a story

Format the response as a complete cover letter without any additional commentary.`

  const userPrompt = `Resume Content:
${resumeContent}

Job Description:
${jobDescription}

Please write a tailored cover letter for this position.`

  try {
    const completion = await openai.chat.completions.create({
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      max_tokens: userTier === 'premium' ? 1000 : 800,
      temperature: 0.7,
    })

    return completion.choices[0]?.message?.content || 'Failed to generate cover letter'
  } catch (error) {
    console.error('OpenAI API error:', error)
    throw new Error('Failed to generate cover letter. Please try again.')
  }
}

export async function improveCoverLetterSection(
  section: string,
  improvement: string,
  userTier: 'free' | 'standard' | 'premium'
): Promise<string> {
  if (userTier === 'free') {
    throw new Error('AI editing assistance is only available for paid subscribers')
  }

  const model = userTier === 'standard' ? 'gpt-4' : 'gpt-4'

  const systemPrompt = `You are an expert writing assistant. Improve the given text section based on the user's request. Maintain the original meaning while enhancing clarity, impact, and professionalism.`

  const userPrompt = `Original text:
${section}

Improvement request:
${improvement}

Please provide the improved version:`

  try {
    const completion = await openai.chat.completions.create({
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      max_tokens: 500,
      temperature: 0.5,
    })

    return completion.choices[0]?.message?.content || 'Failed to improve section'
  } catch (error) {
    console.error('OpenAI API error:', error)
    throw new Error('Failed to improve section. Please try again.')
  }
}
