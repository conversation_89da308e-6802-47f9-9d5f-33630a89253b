'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Header from '@/components/layout/Header'
import { Upload, FileText, Wand2, ArrowLeft } from 'lucide-react'
import { supabase, Resume } from '@/lib/supabase'

export default function CreatePage() {
  const { user, loading, updateCredits } = useAuth()
  const router = useRouter()
  const [step, setStep] = useState(1)
  const [resumes, setResumes] = useState<Resume[]>([])
  const [selectedResume, setSelectedResume] = useState<Resume | null>(null)
  const [jobDescription, setJobDescription] = useState('')
  const [style, setStyle] = useState<'formal' | 'conversational' | 'creative'>('formal')
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchResumes()
    }
  }, [user])

  const fetchResumes = async () => {
    try {
      const { data, error } = await supabase
        .from('resumes')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching resumes:', error)
        return
      }

      setResumes(data || [])
    } catch (error) {
      console.error('Error fetching resumes:', error)
    }
  }

  const handleGenerate = async () => {
    if (!selectedResume || !jobDescription.trim()) {
      setError('Please select a resume and enter a job description')
      return
    }

    if (user?.subscription_tier === 'free' && user.credits_remaining <= 0) {
      setError('You have no credits remaining. Please upgrade your subscription.')
      return
    }

    setGenerating(true)
    setError('')

    try {
      // Call the API route instead of the OpenAI client directly
      const response = await fetch('/api/generate-cover-letter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeContent: selectedResume.content,
          jobDescription,
          style,
          userId: user?.id,
          userTier: user?.subscription_tier || 'free'
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate cover letter')
      }

      const { content: coverLetterContent, creditsRemaining } = await response.json()

      // Save the cover letter
      const { data, error } = await supabase
        .from('cover_letters')
        .insert([
          {
            user_id: user?.id,
            resume_id: selectedResume.id,
            title: `Cover Letter - ${new Date().toLocaleDateString()}`,
            content: coverLetterContent,
            job_description: jobDescription,
            style,
            status: 'completed'
          }
        ])
        .select()
        .single()

      if (error) {
        throw error
      }

      // Update credits for free users (the API already handles this, but we need to update the local state)
      if (user?.subscription_tier === 'free' && creditsRemaining !== null) {
        await updateCredits(creditsRemaining)
      }

      // Redirect to the cover letter editor
      router.push(`/cover-letters/${data.id}`)
    } catch (error: any) {
      setError(error.message || 'Failed to generate cover letter')
    } finally {
      setGenerating(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <button
            onClick={() => router.push('/dashboard')}
            className="btn-tertiary"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </button>
          <div>
            <h1 className="text-heading-1 text-gray-900">Create Cover Letter</h1>
            <p className="text-gray-600">Generate a personalized cover letter with AI</p>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center space-x-4 mb-8">
          <div className={`flex items-center space-x-2 ${step >= 1 ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <span className="font-medium">Select Resume</span>
          </div>
          <div className="flex-1 h-px bg-gray-200"></div>
          <div className={`flex items-center space-x-2 ${step >= 2 ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <span className="font-medium">Job Details</span>
          </div>
          <div className="flex-1 h-px bg-gray-200"></div>
          <div className={`flex items-center space-x-2 ${step >= 3 ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              3
            </div>
            <span className="font-medium">Generate</span>
          </div>
        </div>

        {/* Step Content */}
        <div className="card">
          {step === 1 && (
            <div>
              <h2 className="text-heading-2 text-gray-900 mb-4">Select a Resume</h2>

              {resumes.length === 0 ? (
                <div className="text-center py-8">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No resumes found</h3>
                  <p className="text-gray-600 mb-4">Upload a resume to get started with your cover letter.</p>
                  <button
                    onClick={() => router.push('/resumes')}
                    className="btn-primary"
                  >
                    Upload Resume
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {resumes.map((resume) => (
                    <div
                      key={resume.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedResume?.id === resume.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedResume(resume)}
                    >
                      <div className="flex items-center space-x-3">
                        <FileText className="h-5 w-5 text-gray-400" />
                        <div>
                          <h4 className="font-medium text-gray-900">{resume.title}</h4>
                          <p className="text-sm text-gray-500">
                            Updated {new Date(resume.updated_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}

                  <div className="flex justify-between pt-4">
                    <button
                      onClick={() => router.push('/resumes')}
                      className="btn-secondary"
                    >
                      Manage Resumes
                    </button>
                    <button
                      onClick={() => setStep(2)}
                      disabled={!selectedResume}
                      className="btn-primary"
                    >
                      Continue
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {step === 2 && (
            <div>
              <h2 className="text-heading-2 text-gray-900 mb-4">Job Description & Style</h2>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Job Description
                  </label>
                  <textarea
                    value={jobDescription}
                    onChange={(e) => setJobDescription(e.target.value)}
                    className="input-field h-32"
                    placeholder="Paste the job description here..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Writing Style
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {[
                      { value: 'formal', label: 'Formal', description: 'Professional and traditional' },
                      { value: 'conversational', label: 'Conversational', description: 'Friendly yet professional' },
                      { value: 'creative', label: 'Creative', description: 'Engaging and unique' }
                    ].map((option) => (
                      <div
                        key={option.value}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          style === option.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setStyle(option.value as any)}
                      >
                        <h4 className="font-medium text-gray-900">{option.label}</h4>
                        <p className="text-sm text-gray-500">{option.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex justify-between">
                  <button
                    onClick={() => setStep(1)}
                    className="btn-secondary"
                  >
                    Back
                  </button>
                  <button
                    onClick={() => setStep(3)}
                    disabled={!jobDescription.trim()}
                    className="btn-primary"
                  >
                    Continue
                  </button>
                </div>
              </div>
            </div>
          )}

          {step === 3 && (
            <div>
              <h2 className="text-heading-2 text-gray-900 mb-4">Review & Generate</h2>

              <div className="space-y-4 mb-6">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Selected Resume</h4>
                  <p className="text-gray-600">{selectedResume?.title}</p>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Writing Style</h4>
                  <p className="text-gray-600 capitalize">{style}</p>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Job Description</h4>
                  <p className="text-gray-600 text-sm line-clamp-3">{jobDescription}</p>
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                  {error}
                </div>
              )}

              <div className="flex justify-between">
                <button
                  onClick={() => setStep(2)}
                  className="btn-secondary"
                  disabled={generating}
                >
                  Back
                </button>
                <button
                  onClick={handleGenerate}
                  disabled={generating}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Wand2 className="h-4 w-4" />
                  <span>{generating ? 'Generating...' : 'Generate Cover Letter'}</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
