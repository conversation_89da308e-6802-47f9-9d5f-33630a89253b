{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai'\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n})\n\nexport interface CoverLetterRequest {\n  resumeContent: string\n  jobDescription: string\n  style: 'formal' | 'conversational' | 'creative'\n  userTier: 'free' | 'standard' | 'premium'\n}\n\nexport async function generateCoverLetter({\n  resumeContent,\n  jobDescription,\n  style,\n  userTier\n}: CoverLetterRequest): Promise<string> {\n  const model = userTier === 'free' ? 'gpt-3.5-turbo' : 'gpt-4'\n  \n  const stylePrompts = {\n    formal: 'Write in a professional, formal tone suitable for corporate environments.',\n    conversational: 'Write in a friendly, conversational tone while maintaining professionalism.',\n    creative: 'Write in an engaging, creative tone that showcases personality while remaining professional.'\n  }\n\n  const systemPrompt = `You are an expert cover letter writer. Create a compelling, personalized cover letter based on the provided resume and job description. \n\nGuidelines:\n- ${stylePrompts[style]}\n- Keep it concise (3-4 paragraphs)\n- Highlight relevant skills and experiences from the resume\n- Show enthusiasm for the specific role and company\n- Include a strong opening and closing\n- Make it ATS-friendly\n- Avoid generic phrases and clichés\n- Ensure the letter flows naturally and tells a story\n\nFormat the response as a complete cover letter without any additional commentary.`\n\n  const userPrompt = `Resume Content:\n${resumeContent}\n\nJob Description:\n${jobDescription}\n\nPlease write a tailored cover letter for this position.`\n\n  try {\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        { role: 'system', content: systemPrompt },\n        { role: 'user', content: userPrompt }\n      ],\n      max_tokens: userTier === 'premium' ? 1000 : 800,\n      temperature: 0.7,\n    })\n\n    return completion.choices[0]?.message?.content || 'Failed to generate cover letter'\n  } catch (error) {\n    console.error('OpenAI API error:', error)\n    throw new Error('Failed to generate cover letter. Please try again.')\n  }\n}\n\nexport async function improveCoverLetterSection(\n  section: string,\n  improvement: string,\n  userTier: 'free' | 'standard' | 'premium'\n): Promise<string> {\n  if (userTier === 'free') {\n    throw new Error('AI editing assistance is only available for paid subscribers')\n  }\n\n  const model = userTier === 'standard' ? 'gpt-4' : 'gpt-4'\n\n  const systemPrompt = `You are an expert writing assistant. Improve the given text section based on the user's request. Maintain the original meaning while enhancing clarity, impact, and professionalism.`\n\n  const userPrompt = `Original text:\n${section}\n\nImprovement request:\n${improvement}\n\nPlease provide the improved version:`\n\n  try {\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        { role: 'system', content: systemPrompt },\n        { role: 'user', content: userPrompt }\n      ],\n      max_tokens: 500,\n      temperature: 0.5,\n    })\n\n    return completion.choices[0]?.message?.content || 'Failed to improve section'\n  } catch (error) {\n    console.error('OpenAI API error:', error)\n    throw new Error('Failed to improve section. Please try again.')\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AASO,eAAe,oBAAoB,EACxC,aAAa,EACb,cAAc,EACd,KAAK,EACL,QAAQ,EACW;IACnB,MAAM,QAAQ,aAAa,SAAS,kBAAkB;IAEtD,MAAM,eAAe;QACnB,QAAQ;QACR,gBAAgB;QAChB,UAAU;IACZ;IAEA,MAAM,eAAe,CAAC;;;EAGtB,EAAE,YAAY,CAAC,MAAM,CAAC;;;;;;;;;iFASyD,CAAC;IAEhF,MAAM,aAAa,CAAC;AACtB,EAAE,cAAc;;;AAGhB,EAAE,eAAe;;uDAEsC,CAAC;IAEtD,IAAI;QACF,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAAa;gBACxC;oBAAE,MAAM;oBAAQ,SAAS;gBAAW;aACrC;YACD,YAAY,aAAa,YAAY,OAAO;YAC5C,aAAa;QACf;QAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,0BACpB,OAAe,EACf,WAAmB,EACnB,QAAyC;IAEzC,IAAI,aAAa,QAAQ;QACvB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,aAAa,aAAa,UAAU;IAElD,MAAM,eAAe,CAAC,oLAAoL,CAAC;IAE3M,MAAM,aAAa,CAAC;AACtB,EAAE,QAAQ;;;AAGV,EAAE,YAAY;;oCAEsB,CAAC;IAEnC,IAAI;QACF,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAAa;gBACxC;oBAAE,MAAM;oBAAQ,SAAS;gBAAW;aACrC;YACD,YAAY;YACZ,aAAa;QACf;QAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/app/api/generate-cover-letter/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { generateCoverLetter } from '@/lib/openai'\nimport { createClient } from '@supabase/supabase-js'\n\n// Create a server-side Supabase client with service role key to bypass RLS\n// If service role key is not available, we'll handle the error gracefully\nconst supabaseAdmin = createClient(\n  process.env.NEXT_PUBLIC_SUPABASE_URL!,\n  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n)\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { resumeContent, jobDescription, style, userId, userTier, resumeId } = await request.json()\n\n    // Validate required fields\n    if (!resumeContent || !jobDescription || !userId || !resumeId) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      )\n    }\n\n    // Check user's subscription and credits\n    console.log('Looking up user with ID:', userId)\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('subscription_tier, credits_remaining')\n      .eq('id', userId)\n      .single()\n\n    if (userError) {\n      console.error('User lookup error:', {\n        error: userError,\n        code: userError.code,\n        message: userError.message,\n        details: userError.details,\n        hint: userError.hint,\n        userId\n      })\n      return NextResponse.json(\n        { error: `User lookup failed: ${userError.message}` },\n        { status: 404 }\n      )\n    }\n\n    if (!user) {\n      console.error('No user found with ID:', userId)\n      return NextResponse.json(\n        { error: 'User not found' },\n        { status: 404 }\n      )\n    }\n\n    console.log('User found:', { id: userId, tier: user.subscription_tier, credits: user.credits_remaining })\n\n    // Check if free user has credits\n    if (user.subscription_tier === 'free' && user.credits_remaining <= 0) {\n      return NextResponse.json(\n        { error: 'No credits remaining. Please upgrade your subscription.' },\n        { status: 403 }\n      )\n    }\n\n    // Generate cover letter\n    console.log('Generating cover letter...')\n    const coverLetterContent = await generateCoverLetter({\n      resumeContent,\n      jobDescription,\n      style: style || 'formal',\n      userTier: userTier || user.subscription_tier\n    })\n\n    console.log('Cover letter generated successfully')\n\n    // Save the cover letter to the database\n    const { data: coverLetter, error: saveError } = await supabaseAdmin\n      .from('cover_letters')\n      .insert([\n        {\n          user_id: userId,\n          resume_id: resumeId,\n          title: `Cover Letter - ${new Date().toLocaleDateString()}`,\n          content: coverLetterContent,\n          job_description: jobDescription,\n          style: style || 'formal',\n          status: 'completed'\n        }\n      ])\n      .select()\n      .single()\n\n    if (saveError) {\n      console.error('Error saving cover letter:', saveError)\n      return NextResponse.json(\n        { error: 'Failed to save cover letter' },\n        { status: 500 }\n      )\n    }\n\n    console.log('Cover letter saved with ID:', coverLetter.id)\n\n    // Update user credits if free tier\n    if (user.subscription_tier === 'free') {\n      const { error: updateError } = await supabaseAdmin\n        .from('users')\n        .update({ credits_remaining: user.credits_remaining - 1 })\n        .eq('id', userId)\n\n      if (updateError) {\n        console.error('Error updating credits:', updateError)\n      }\n\n      // Log usage\n      await supabaseAdmin\n        .from('user_usage')\n        .insert([\n          {\n            user_id: userId,\n            action: 'cover_letter_generated',\n            credits_used: 1\n          }\n        ])\n    }\n\n    return NextResponse.json({\n      content: coverLetterContent,\n      coverLetterId: coverLetter.id,\n      creditsRemaining: user.subscription_tier === 'free' ? user.credits_remaining - 1 : null\n    })\n\n  } catch (error: any) {\n    console.error('Error generating cover letter:', error)\n    return NextResponse.json(\n      { error: error.message || 'Failed to generate cover letter' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,2EAA2E;AAC3E,0EAA0E;AAC1E,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE/B,QAAQ,GAAG,CAAC,yBAAyB,0PACrC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAGK,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/F,2BAA2B;QAC3B,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU;YAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,QAAQ,GAAG,CAAC,4BAA4B;QACxC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,wCACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,sBAAsB;gBAClC,OAAO;gBACP,MAAM,UAAU,IAAI;gBACpB,SAAS,UAAU,OAAO;gBAC1B,SAAS,UAAU,OAAO;gBAC1B,MAAM,UAAU,IAAI;gBACpB;YACF;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,oBAAoB,EAAE,UAAU,OAAO,EAAE;YAAC,GACpD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,MAAM;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,eAAe;YAAE,IAAI;YAAQ,MAAM,KAAK,iBAAiB;YAAE,SAAS,KAAK,iBAAiB;QAAC;QAEvG,iCAAiC;QACjC,IAAI,KAAK,iBAAiB,KAAK,UAAU,KAAK,iBAAiB,IAAI,GAAG;YACpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0D,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM,qBAAqB,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;YACnD;YACA;YACA,OAAO,SAAS;YAChB,UAAU,YAAY,KAAK,iBAAiB;QAC9C;QAEA,QAAQ,GAAG,CAAC;QAEZ,wCAAwC;QACxC,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cACnD,IAAI,CAAC,iBACL,MAAM,CAAC;YACN;gBACE,SAAS;gBACT,WAAW;gBACX,OAAO,CAAC,eAAe,EAAE,IAAI,OAAO,kBAAkB,IAAI;gBAC1D,SAAS;gBACT,iBAAiB;gBACjB,OAAO,SAAS;gBAChB,QAAQ;YACV;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,+BAA+B,YAAY,EAAE;QAEzD,mCAAmC;QACnC,IAAI,KAAK,iBAAiB,KAAK,QAAQ;YACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAClC,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE,mBAAmB,KAAK,iBAAiB,GAAG;YAAE,GACvD,EAAE,CAAC,MAAM;YAEZ,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;YAEA,YAAY;YACZ,MAAM,cACH,IAAI,CAAC,cACL,MAAM,CAAC;gBACN;oBACE,SAAS;oBACT,QAAQ;oBACR,cAAc;gBAChB;aACD;QACL;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,eAAe,YAAY,EAAE;YAC7B,kBAAkB,KAAK,iBAAiB,KAAK,SAAS,KAAK,iBAAiB,GAAG,IAAI;QACrF;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,MAAM,OAAO,IAAI;QAAkC,GAC5D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}