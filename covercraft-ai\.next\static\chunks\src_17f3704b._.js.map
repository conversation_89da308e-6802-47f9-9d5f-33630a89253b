{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  subscription_tier: 'free' | 'standard' | 'premium'\n  credits_remaining: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface Resume {\n  id: string\n  user_id: string\n  title: string\n  content: string\n  file_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CoverLetter {\n  id: string\n  user_id: string\n  resume_id?: string\n  title: string\n  content: string\n  job_description?: string\n  style: 'formal' | 'conversational' | 'creative'\n  status: 'draft' | 'completed'\n  created_at: string\n  updated_at: string\n}\n\nexport interface Template {\n  id: string\n  title: string\n  description: string\n  content: string\n  category: string\n  is_premium: boolean\n  created_at: string\n}\n\nexport interface UserUsage {\n  id: string\n  user_id: string\n  action: 'cover_letter_generated' | 'credit_used'\n  credits_used: number\n  created_at: string\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/CoverCraft%20AI/covercraft-ai/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User as SupabaseUser } from '@supabase/supabase-js'\nimport { supabase, User } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  supabaseUser: SupabaseUser | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<void>\n  signUp: (email: string, password: string, fullName: string) => Promise<void>\n  signOut: () => Promise<void>\n  updateCredits: (newCredits: number) => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSupabaseUser(session?.user ?? null)\n      if (session?.user) {\n        fetchUserProfile(session.user.id)\n      } else {\n        setLoading(false)\n      }\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSupabaseUser(session?.user ?? null)\n      if (session?.user) {\n        await fetchUserProfile(session.user.id)\n      } else {\n        setUser(null)\n        setLoading(false)\n      }\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchUserProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching user profile:', {\n          error,\n          code: error.code,\n          message: error.message,\n          details: error.details,\n          hint: error.hint,\n          userId\n        })\n\n        // If user profile doesn't exist, create one\n        if (error.code === 'PGRST116') {\n          console.log('User profile not found, creating one...')\n          await createUserProfile(userId)\n          return\n        }\n\n        setLoading(false)\n        return\n      }\n\n      setUser(data)\n    } catch (error) {\n      console.error('Error fetching user profile:', {\n        error,\n        userId,\n        errorType: typeof error,\n        errorMessage: error instanceof Error ? error.message : 'Unknown error'\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const createUserProfile = async (userId: string) => {\n    try {\n      // Get the current user's email from Supabase auth\n      const { data: authUser } = await supabase.auth.getUser()\n\n      if (!authUser.user) {\n        console.error('No authenticated user found')\n        return\n      }\n\n      const { data, error } = await supabase\n        .from('users')\n        .insert([\n          {\n            id: userId,\n            email: authUser.user.email!,\n            full_name: authUser.user.user_metadata?.full_name || null,\n            subscription_tier: 'free',\n            credits_remaining: 3,\n          },\n        ])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating user profile:', {\n          error,\n          code: error.code,\n          message: error.message,\n          details: error.details,\n          hint: error.hint,\n          userId\n        })\n        return\n      }\n\n      console.log('User profile created successfully:', data)\n      setUser(data)\n    } catch (error) {\n      console.error('Error in createUserProfile:', {\n        error,\n        userId,\n        errorType: typeof error,\n        errorMessage: error instanceof Error ? error.message : 'Unknown error'\n      })\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n\n    if (error) {\n      throw error\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n\n    if (error) {\n      throw error\n    }\n\n    // Create user profile\n    if (data.user) {\n      const { error: profileError } = await supabase\n        .from('users')\n        .insert([\n          {\n            id: data.user.id,\n            email: data.user.email,\n            full_name: fullName,\n            subscription_tier: 'free',\n            credits_remaining: 3,\n          },\n        ])\n\n      if (profileError) {\n        console.error('Error creating user profile during signup:', {\n          error: profileError,\n          code: profileError.code,\n          message: profileError.message,\n          details: profileError.details,\n          hint: profileError.hint,\n          userId: data.user.id\n        })\n        // Don't throw here as the auth user was created successfully\n        // The profile will be created later when fetchUserProfile is called\n      }\n    }\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) {\n      throw error\n    }\n  }\n\n  const updateCredits = async (newCredits: number) => {\n    if (!user) return\n\n    const { error } = await supabase\n      .from('users')\n      .update({ credits_remaining: newCredits })\n      .eq('id', user.id)\n\n    if (error) {\n      console.error('Error updating credits:', error)\n      throw error\n    }\n\n    setUser({ ...user, credits_remaining: newCredits })\n  }\n\n  const value = {\n    user,\n    supabaseUser,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    updateCredits,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;oBACpD,gBAAgB,SAAS,QAAQ;oBACjC,IAAI,SAAS,MAAM;wBACjB,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBAClC,OAAO;wBACL,WAAW;oBACb;gBACF;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;oBAChD,gBAAgB,SAAS,QAAQ;oBACjC,IAAI,SAAS,MAAM;wBACjB,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBACxC,OAAO;wBACL,QAAQ;wBACR,WAAW;oBACb;gBACF;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;oBAC5C;oBACA,MAAM,MAAM,IAAI;oBAChB,SAAS,MAAM,OAAO;oBACtB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB;gBACF;gBAEA,4CAA4C;gBAC5C,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B,QAAQ,GAAG,CAAC;oBACZ,MAAM,kBAAkB;oBACxB;gBACF;gBAEA,WAAW;gBACX;YACF;YAEA,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC5C;gBACA;gBACA,WAAW,OAAO;gBAClB,cAAc,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,kDAAkD;YAClD,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAEtD,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN;oBACE,IAAI;oBACJ,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,WAAW,SAAS,IAAI,CAAC,aAAa,EAAE,aAAa;oBACrD,mBAAmB;oBACnB,mBAAmB;gBACrB;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;oBAC5C;oBACA,MAAM,MAAM,IAAI;oBAChB,SAAS,MAAM,OAAO;oBACtB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB;gBACF;gBACA;YACF;YAEA,QAAQ,GAAG,CAAC,sCAAsC;YAClD,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC3C;gBACA;gBACA,WAAW,OAAO;gBAClB,cAAc,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD;QACF;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QAEA,IAAI,OAAO;YACT,MAAM;QACR;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QAEA,IAAI,OAAO;YACT,MAAM;QACR;QAEA,sBAAsB;QACtB,IAAI,KAAK,IAAI,EAAE;YACb,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,SACL,MAAM,CAAC;gBACN;oBACE,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,WAAW;oBACX,mBAAmB;oBACnB,mBAAmB;gBACrB;aACD;YAEH,IAAI,cAAc;gBAChB,QAAQ,KAAK,CAAC,8CAA8C;oBAC1D,OAAO;oBACP,MAAM,aAAa,IAAI;oBACvB,SAAS,aAAa,OAAO;oBAC7B,SAAS,aAAa,OAAO;oBAC7B,MAAM,aAAa,IAAI;oBACvB,QAAQ,KAAK,IAAI,CAAC,EAAE;gBACtB;YACA,6DAA6D;YAC7D,oEAAoE;YACtE;QACF;IACF;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO;YACT,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM;QAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,mBAAmB;QAAW,GACvC,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;QAEA,QAAQ;YAAE,GAAG,IAAI;YAAE,mBAAmB;QAAW;IACnD;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAnNgB;KAAA;AAqNT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}