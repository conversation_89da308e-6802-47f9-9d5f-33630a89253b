-- Fix Row Level Security (RLS) Configuration
-- Run this SQL in your Supabase SQL editor to fix RLS issues

-- First, ensure RLS is enabled on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resumes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cover_letters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_usage ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;

-- Users policies (fixed to include INSERT)
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- IMPORTANT: Add INSERT policy for users table
CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Verify templates policy allows public read access
DROP POLICY IF EXISTS "Anyone can view templates" ON public.templates;
CREATE POLICY "Anyone can view templates" ON public.templates
    FOR SELECT USING (true);

-- Check current policies
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    roles, 
    cmd, 
    qual
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, cmd;

-- Test RLS configuration
-- These queries should fail for authenticated users trying to access other users' data
-- and succeed for users accessing their own data

-- Check if RLS is properly enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'resumes', 'cover_letters', 'templates', 'user_usage');
