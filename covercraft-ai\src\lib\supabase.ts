import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  subscription_tier: 'free' | 'standard' | 'premium'
  credits_remaining: number
  created_at: string
  updated_at: string
}

export interface Resume {
  id: string
  user_id: string
  title: string
  content: string
  file_url?: string
  created_at: string
  updated_at: string
}

export interface CoverLetter {
  id: string
  user_id: string
  resume_id?: string
  title: string
  content: string
  job_description?: string
  style: 'formal' | 'conversational' | 'creative'
  status: 'draft' | 'completed'
  created_at: string
  updated_at: string
}

export interface Template {
  id: string
  title: string
  description: string
  content: string
  category: string
  is_premium: boolean
  created_at: string
}

export interface UserUsage {
  id: string
  user_id: string
  action: 'cover_letter_generated' | 'credit_used'
  credits_used: number
  created_at: string
}
