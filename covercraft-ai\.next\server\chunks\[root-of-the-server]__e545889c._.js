module.exports = {

"[project]/.next-internal/server/app/api/generate-cover-letter/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/node:stream/web [external] (node:stream/web, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream/web", () => require("node:stream/web"));

module.exports = mod;
}}),
"[project]/src/lib/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCoverLetter": (()=>generateCoverLetter),
    "improveCoverLetterSection": (()=>improveCoverLetterSection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
async function generateCoverLetter({ resumeContent, jobDescription, style, userTier }) {
    const model = userTier === 'free' ? 'gpt-3.5-turbo' : 'gpt-4';
    const stylePrompts = {
        formal: 'Write in a professional, formal tone suitable for corporate environments.',
        conversational: 'Write in a friendly, conversational tone while maintaining professionalism.',
        creative: 'Write in an engaging, creative tone that showcases personality while remaining professional.'
    };
    const systemPrompt = `You are an expert cover letter writer. Create a compelling, personalized cover letter based on the provided resume and job description. 

Guidelines:
- ${stylePrompts[style]}
- Keep it concise (3-4 paragraphs)
- Highlight relevant skills and experiences from the resume
- Show enthusiasm for the specific role and company
- Include a strong opening and closing
- Make it ATS-friendly
- Avoid generic phrases and clichés
- Ensure the letter flows naturally and tells a story

Format the response as a complete cover letter without any additional commentary.`;
    const userPrompt = `Resume Content:
${resumeContent}

Job Description:
${jobDescription}

Please write a tailored cover letter for this position.`;
    try {
        const completion = await openai.chat.completions.create({
            model,
            messages: [
                {
                    role: 'system',
                    content: systemPrompt
                },
                {
                    role: 'user',
                    content: userPrompt
                }
            ],
            max_tokens: userTier === 'premium' ? 1000 : 800,
            temperature: 0.7
        });
        return completion.choices[0]?.message?.content || 'Failed to generate cover letter';
    } catch (error) {
        console.error('OpenAI API error:', error);
        throw new Error('Failed to generate cover letter. Please try again.');
    }
}
async function improveCoverLetterSection(section, improvement, userTier) {
    if (userTier === 'free') {
        throw new Error('AI editing assistance is only available for paid subscribers');
    }
    const model = userTier === 'standard' ? 'gpt-4' : 'gpt-4';
    const systemPrompt = `You are an expert writing assistant. Improve the given text section based on the user's request. Maintain the original meaning while enhancing clarity, impact, and professionalism.`;
    const userPrompt = `Original text:
${section}

Improvement request:
${improvement}

Please provide the improved version:`;
    try {
        const completion = await openai.chat.completions.create({
            model,
            messages: [
                {
                    role: 'system',
                    content: systemPrompt
                },
                {
                    role: 'user',
                    content: userPrompt
                }
            ],
            max_tokens: 500,
            temperature: 0.5
        });
        return completion.choices[0]?.message?.content || 'Failed to improve section';
    } catch (error) {
        console.error('OpenAI API error:', error);
        throw new Error('Failed to improve section. Please try again.');
    }
}
}}),
"[project]/src/app/api/generate-cover-letter/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
;
;
// Create a server-side Supabase client with service role key to bypass RLS
// If service role key is not available, we'll handle the error gracefully
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://yqhfcbbmauefjcslqnkh.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY || ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxaGZjYmJtYXVlZmpjc2xxbmtoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxMzM5MzAsImV4cCI6MjA2MzcwOTkzMH0.yrTmSR0rSWPDkRHeVBP_YU8opXuhLHb-hd41ZW3gnbI"), {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});
async function POST(request) {
    try {
        const { resumeContent, jobDescription, style, userId, userTier, resumeId } = await request.json();
        // Validate required fields
        if (!resumeContent || !jobDescription || !userId || !resumeId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields'
            }, {
                status: 400
            });
        }
        // Check user's subscription and credits
        console.log('Looking up user with ID:', userId);
        const { data: user, error: userError } = await supabaseAdmin.from('users').select('subscription_tier, credits_remaining').eq('id', userId).single();
        if (userError) {
            console.error('User lookup error:', {
                error: userError,
                code: userError.code,
                message: userError.message,
                details: userError.details,
                hint: userError.hint,
                userId
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `User lookup failed: ${userError.message}`
            }, {
                status: 404
            });
        }
        if (!user) {
            console.error('No user found with ID:', userId);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'User not found'
            }, {
                status: 404
            });
        }
        console.log('User found:', {
            id: userId,
            tier: user.subscription_tier,
            credits: user.credits_remaining
        });
        // Check if free user has credits
        if (user.subscription_tier === 'free' && user.credits_remaining <= 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No credits remaining. Please upgrade your subscription.'
            }, {
                status: 403
            });
        }
        // Generate cover letter
        console.log('Generating cover letter...');
        const coverLetterContent = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateCoverLetter"])({
            resumeContent,
            jobDescription,
            style: style || 'formal',
            userTier: userTier || user.subscription_tier
        });
        console.log('Cover letter generated successfully');
        // Save the cover letter to the database
        const { data: coverLetter, error: saveError } = await supabaseAdmin.from('cover_letters').insert([
            {
                user_id: userId,
                resume_id: resumeId,
                title: `Cover Letter - ${new Date().toLocaleDateString()}`,
                content: coverLetterContent,
                job_description: jobDescription,
                style: style || 'formal',
                status: 'completed'
            }
        ]).select().single();
        if (saveError) {
            console.error('Error saving cover letter:', saveError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to save cover letter'
            }, {
                status: 500
            });
        }
        console.log('Cover letter saved with ID:', coverLetter.id);
        // Update user credits if free tier
        if (user.subscription_tier === 'free') {
            const { error: updateError } = await supabaseAdmin.from('users').update({
                credits_remaining: user.credits_remaining - 1
            }).eq('id', userId);
            if (updateError) {
                console.error('Error updating credits:', updateError);
            }
            // Log usage
            await supabaseAdmin.from('user_usage').insert([
                {
                    user_id: userId,
                    action: 'cover_letter_generated',
                    credits_used: 1
                }
            ]);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            content: coverLetterContent,
            coverLetterId: coverLetter.id,
            creditsRemaining: user.subscription_tier === 'free' ? user.credits_remaining - 1 : null
        });
    } catch (error) {
        console.error('Error generating cover letter:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error.message || 'Failed to generate cover letter'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e545889c._.js.map