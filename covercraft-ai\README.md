# CoverCraft AI

An AI-powered cover letter generator that helps job seekers create professional, personalized cover letters in minutes.

## Features

- **AI-Powered Generation**: Uses OpenAI GPT models to create personalized cover letters
- **Multiple Writing Styles**: Formal, conversational, and creative options
- **Resume Integration**: Upload and manage multiple resumes
- **Export Options**: PDF, Word, and text formats
- **Subscription Tiers**: Free, Standard, and Premium plans
- **Template Library**: Industry-specific templates
- **Credit System**: Track usage for free tier users

## Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI**: OpenAI GPT API
- **PDF Generation**: jsPDF
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account
- OpenAI API key

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables in `.env.local`:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Database Setup

1. Create a new Supabase project
2. Get your Supabase URL and anon key from the project settings
3. Update your `.env.local` file with the correct values
4. Run the SQL schema in your Supabase SQL editor (see `supabase-schema.sql`)
5. Enable Row Level Security in your Supabase dashboard

#### Quick Database Setup

Use our helper scripts to set up and verify your database:

```bash
# Set up the database (provides instructions)
npm run db:setup

# Verify database setup
npm run db:verify
```

#### Manual Database Setup

1. Open your Supabase project dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `supabase-schema.sql`
4. Run the SQL script
5. Verify the setup with `npm run db:verify`

### Running the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Troubleshooting

### "Error fetching user profile" Issues

If you see errors like "Error fetching user profile: {}", this usually means:

1. **Row Level Security (RLS) not configured**: Most common cause
2. **Database tables don't exist**: Run `npm run db:verify` to check
3. **Missing environment variables**: Check your `.env.local` file
4. **User profile not created**: The app will automatically create missing profiles

#### Quick Fix for RLS Issues

**This is the most common cause of the error.** Run this diagnostic:

```bash
npm run db:check-rls
```

If you see "RLS issue: Query succeeded without authentication", follow these steps:

1. **Open your Supabase project dashboard**
2. **Go to the SQL Editor**
3. **Copy and paste the contents of `scripts/fix-rls.sql`**
4. **Run the SQL script**
5. **Verify the fix with `npm run db:check-rls`**

#### Other Solutions

1. **Verify database setup**:
   ```bash
   npm run db:verify
   ```

2. **Test authentication flow**:
   ```bash
   npm run test:auth
   ```

3. **Check environment variables**:
   - Ensure `NEXT_PUBLIC_SUPABASE_URL` is set
   - Ensure `NEXT_PUBLIC_SUPABASE_ANON_KEY` is set
   - Values should not contain placeholder text

4. **Reset user profile** (if needed):
   - Go to your Supabase dashboard
   - Navigate to Table Editor > users
   - Delete the problematic user record
   - The app will recreate it on next login

5. **Check browser console** for detailed error messages with error codes

### Database Connection Issues

- Verify your Supabase project is active
- Check that your API keys are correct
- Ensure your project URL is correct (should end with `.supabase.co`)
- Make sure Row Level Security is enabled on all tables

## Subscription Tiers

### Free Tier (Explorer)
- 3 cover letter credits
- Basic AI generation (GPT-3.5)
- PDF export
- 1 resume storage

### Standard Tier (Professional) - $5/month
- Unlimited cover letters
- Advanced AI (GPT-4)
- Multiple export formats
- 5 resume storage
- AI editing assistance

### Premium Tier (Executive) - $8/month
- All Standard features
- Priority processing
- Unlimited resume storage
- Advanced customization
- Priority support

## Deployment

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
