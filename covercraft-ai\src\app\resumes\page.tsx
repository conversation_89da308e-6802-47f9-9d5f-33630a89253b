'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Header from '@/components/layout/Header'
import { Upload, FileText, Trash2, Edit, ArrowLeft, Plus } from 'lucide-react'
import { supabase, Resume } from '@/lib/supabase'

export default function ResumesPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [resumes, setResumes] = useState<Resume[]>([])
  const [loadingResumes, setLoadingResumes] = useState(true)
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [newResume, setNewResume] = useState({ title: '', content: '' })

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchResumes()
    }
  }, [user])

  const fetchResumes = async () => {
    try {
      const { data, error } = await supabase
        .from('resumes')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching resumes:', error)
        return
      }

      setResumes(data || [])
    } catch (error) {
      console.error('Error fetching resumes:', error)
    } finally {
      setLoadingResumes(false)
    }
  }

  const handleUploadResume = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newResume.title.trim() || !newResume.content.trim()) {
      return
    }

    setUploading(true)

    try {
      const { data, error } = await supabase
        .from('resumes')
        .insert([
          {
            user_id: user?.id,
            title: newResume.title,
            content: newResume.content
          }
        ])
        .select()
        .single()

      if (error) {
        throw error
      }

      setResumes([data, ...resumes])
      setNewResume({ title: '', content: '' })
      setShowUploadForm(false)
    } catch (error) {
      console.error('Error uploading resume:', error)
    } finally {
      setUploading(false)
    }
  }

  const handleDeleteResume = async (resumeId: string) => {
    if (!confirm('Are you sure you want to delete this resume?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('resumes')
        .delete()
        .eq('id', resumeId)

      if (error) {
        throw error
      }

      setResumes(resumes.filter(resume => resume.id !== resumeId))
    } catch (error) {
      console.error('Error deleting resume:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/dashboard')}
              className="btn-tertiary"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </button>
            <div>
              <h1 className="text-heading-1 text-gray-900">Manage Resumes</h1>
              <p className="text-gray-600">Upload and organize your resumes</p>
            </div>
          </div>
          <button
            onClick={() => setShowUploadForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Resume</span>
          </button>
        </div>

        {/* Upload Form Modal */}
        {showUploadForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="card max-w-2xl w-full mx-4">
              <h2 className="text-heading-2 text-gray-900 mb-4">Add New Resume</h2>
              <form onSubmit={handleUploadResume} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resume Title
                  </label>
                  <input
                    type="text"
                    value={newResume.title}
                    onChange={(e) => setNewResume({ ...newResume, title: e.target.value })}
                    className="input-field"
                    placeholder="e.g., Software Engineer Resume 2024"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resume Content
                  </label>
                  <textarea
                    value={newResume.content}
                    onChange={(e) => setNewResume({ ...newResume, content: e.target.value })}
                    className="input-field h-64"
                    placeholder="Paste your resume content here..."
                    required
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowUploadForm(false)}
                    className="btn-secondary"
                    disabled={uploading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={uploading}
                  >
                    {uploading ? 'Saving...' : 'Save Resume'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Resumes List */}
        <div className="card">
          {loadingResumes ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading resumes...</p>
            </div>
          ) : resumes.length === 0 ? (
            <div className="text-center py-12">
              <Upload className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No resumes yet</h3>
              <p className="text-gray-600 mb-6">Upload your first resume to start creating cover letters.</p>
              <button
                onClick={() => setShowUploadForm(true)}
                className="btn-primary"
              >
                Upload Resume
              </button>
            </div>
          ) : (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-heading-2 text-gray-900">Your Resumes</h2>
                <p className="text-sm text-gray-500">
                  {resumes.length} of {user.subscription_tier === 'free' ? '1' : user.subscription_tier === 'standard' ? '5' : 'unlimited'} resumes
                </p>
              </div>
              
              <div className="space-y-4">
                {resumes.map((resume) => (
                  <div
                    key={resume.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <FileText className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{resume.title}</h4>
                        <p className="text-sm text-gray-500">
                          Created {formatDate(resume.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setNewResume({ title: resume.title, content: resume.content })
                          setShowUploadForm(true)
                        }}
                        className="btn-tertiary"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteResume(resume.id)}
                        className="btn-tertiary text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
