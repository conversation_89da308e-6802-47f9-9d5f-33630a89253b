#!/usr/bin/env node

/**
 * Row Level Security (RLS) Check Script
 * 
 * This script checks if <PERSON><PERSON> is properly configured and provides
 * instructions to fix common RLS issues.
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Create service client if available
let supabaseService = null
if (supabaseServiceKey && supabaseServiceKey !== 'your_supabase_service_role_key_here') {
  supabaseService = createClient(supabaseUrl, supabaseServiceKey)
}

async function checkRLS() {
  console.log('🔒 Checking Row Level Security configuration...\n')
  
  const tables = ['users', 'resumes', 'cover_letters', 'templates', 'user_usage']
  
  for (const table of tables) {
    console.log(`📋 Checking table: ${table}`)
    
    // Test with anon key (should be restricted)
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1)
      
      if (error) {
        if (error.code === '42501') {
          console.log(`   ✅ RLS properly configured (access denied)`)
        } else {
          console.log(`   ❌ Unexpected error: ${error.message}`)
        }
      } else {
        console.log(`   ⚠️  RLS issue: Query succeeded without authentication`)
        console.log(`      Found ${data.length} records`)
        console.log(`      → RLS might not be enabled or policies are too permissive`)
      }
    } catch (error) {
      console.log(`   ❌ Query failed: ${error.message}`)
    }
    
    // Test with service key if available
    if (supabaseService) {
      try {
        const { data, error } = await supabaseService.from(table).select('count', { count: 'exact', head: true })
        if (!error) {
          console.log(`   ✅ Service key access works`)
        } else {
          console.log(`   ❌ Service key access failed: ${error.message}`)
        }
      } catch (error) {
        console.log(`   ❌ Service key test failed: ${error.message}`)
      }
    }
    
    console.log('')
  }
  
  // Provide RLS fix instructions
  console.log('🔧 RLS Configuration Instructions:')
  console.log('')
  console.log('If RLS is not properly configured, follow these steps:')
  console.log('')
  console.log('1. Open your Supabase project dashboard')
  console.log('2. Go to Authentication > Policies')
  console.log('3. For each table, ensure RLS is enabled')
  console.log('4. Check that the policies match the schema file')
  console.log('')
  console.log('Or run this SQL in your Supabase SQL editor:')
  console.log('')
  console.log('-- Enable RLS on all tables')
  console.log('ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;')
  console.log('ALTER TABLE public.resumes ENABLE ROW LEVEL SECURITY;')
  console.log('ALTER TABLE public.cover_letters ENABLE ROW LEVEL SECURITY;')
  console.log('ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;')
  console.log('ALTER TABLE public.user_usage ENABLE ROW LEVEL SECURITY;')
  console.log('')
  console.log('-- Check if policies exist')
  console.log('SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual')
  console.log('FROM pg_policies')
  console.log('WHERE schemaname = \'public\';')
  console.log('')
  
  if (!supabaseService) {
    console.log('💡 Tip: Add SUPABASE_SERVICE_ROLE_KEY to .env.local for more detailed testing')
  }
}

checkRLS().catch(console.error)
