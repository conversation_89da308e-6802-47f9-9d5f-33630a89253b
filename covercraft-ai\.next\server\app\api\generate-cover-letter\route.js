const CHUNK_PUBLIC_PATH = "server/app/api/generate-cover-letter/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_1e0a538a._.js");
runtime.loadChunk("server/chunks/node_modules_next_f33e9d37._.js");
runtime.loadChunk("server/chunks/node_modules_openai_a822ecb7._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_fbd19a5c._.js");
runtime.loadChunk("server/chunks/node_modules_d4768df0._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__377870b1._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/generate-cover-letter/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-cover-letter/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/generate-cover-letter/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
