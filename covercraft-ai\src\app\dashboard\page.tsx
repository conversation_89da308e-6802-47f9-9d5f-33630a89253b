'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Header from '@/components/layout/Header'
import { Plus, FileText, Clock, Download } from 'lucide-react'
import { supabase, CoverLetter } from '@/lib/supabase'

export default function DashboardPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [coverLetters, setCoverLetters] = useState<CoverLetter[]>([])
  const [loadingLetters, setLoadingLetters] = useState(true)

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchCoverLetters()
    }
  }, [user])

  const fetchCoverLetters = async () => {
    try {
      const { data, error } = await supabase
        .from('cover_letters')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) {
        console.error('Error fetching cover letters:', error)
        return
      }

      setCoverLetters(data || [])
    } catch (error) {
      console.error('Error fetching cover letters:', error)
    } finally {
      setLoadingLetters(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-heading-1 text-gray-900 mb-2">
            Welcome back, {user.full_name?.split(' ')[0] || 'there'}!
          </h1>
          <p className="text-gray-600">
            Ready to create your next professional cover letter?
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <button
            onClick={() => router.push('/create')}
            className="card hover:shadow-md transition-shadow duration-200 text-left group"
          >
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <Plus className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-heading-3 text-gray-900">Create Cover Letter</h3>
                <p className="text-sm text-gray-600">Start with AI assistance</p>
              </div>
            </div>
          </button>

          <button
            onClick={() => router.push('/templates')}
            className="card hover:shadow-md transition-shadow duration-200 text-left group"
          >
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-heading-3 text-gray-900">Browse Templates</h3>
                <p className="text-sm text-gray-600">Industry-specific templates</p>
              </div>
            </div>
          </button>

          <button
            onClick={() => router.push('/resumes')}
            className="card hover:shadow-md transition-shadow duration-200 text-left group"
          >
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="text-heading-3 text-gray-900">Manage Resumes</h3>
                <p className="text-sm text-gray-600">Upload and organize</p>
              </div>
            </div>
          </button>
        </div>

        {/* Recent Cover Letters */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-heading-2 text-gray-900">Recent Cover Letters</h2>
            <button
              onClick={() => router.push('/cover-letters')}
              className="btn-tertiary"
            >
              View all
            </button>
          </div>

          {loadingLetters ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading cover letters...</p>
            </div>
          ) : coverLetters.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No cover letters yet</h3>
              <p className="text-gray-600 mb-4">Create your first AI-powered cover letter to get started.</p>
              <button
                onClick={() => router.push('/create')}
                className="btn-primary"
              >
                Create Cover Letter
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {coverLetters.map((letter) => (
                <div
                  key={letter.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{letter.title}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{formatDate(letter.created_at)}</span>
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          letter.status === 'completed' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {letter.status}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => router.push(`/cover-letters/${letter.id}`)}
                      className="btn-tertiary"
                    >
                      Edit
                    </button>
                    <button className="btn-tertiary">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
